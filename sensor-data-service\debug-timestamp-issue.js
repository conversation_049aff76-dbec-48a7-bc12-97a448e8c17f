#!/usr/bin/env node

/**
 * Debug Timestamp Issue
 * 
 * This script investigates why Sensor_Data is not maintaining the 5-minute
 * gap pattern from hall data and why there are multiple entries around 12:08.
 */

const { getDatabase } = require('./firebase');

async function debugTimestampIssue() {
  console.log('🔍 Debugging Timestamp Issue...\n');
  
  try {
    const db = getDatabase();
    const targetDate = '2025-07-20';
    const category = 'Basement';
    
    // Get hall data for the problematic time period
    console.log('📊 Checking hall data around 12:08...');
    const hallRef = db.ref(`hall/data/${targetDate}`);
    const hallSnapshot = await hallRef.once('value');
    
    if (!hallSnapshot.exists()) {
      console.log('❌ No hall data found');
      return;
    }
    
    const hallData = hallSnapshot.val();
    const hallTimestamps = Object.keys(hallData).sort();
    
    // Filter timestamps around 12:08
    const problematicHallTimes = hallTimestamps.filter(t => t.startsWith('12:08'));
    console.log(`📊 Hall timestamps around 12:08: ${problematicHallTimes.length}`);
    problematicHallTimes.forEach(time => {
      const data = hallData[time];
      console.log(`   ${time} - timestamp: "${data.timestamp}"`);
    });
    
    // Get Sensor_Data for the same period
    console.log('\n📊 Checking Sensor_Data around 12:08...');
    const sensorDataRef = db.ref(`Sensor_Data/${category}/${targetDate}`);
    const sensorDataSnapshot = await sensorDataRef.once('value');
    
    if (!sensorDataSnapshot.exists()) {
      console.log('❌ No Sensor_Data found');
      return;
    }
    
    const sensorData = sensorDataSnapshot.val();
    const sensorTimestamps = Object.keys(sensorData).sort();
    
    // Filter timestamps around 12:08
    const problematicSensorTimes = sensorTimestamps.filter(t => t.startsWith('12:08'));
    console.log(`📊 Sensor_Data timestamps around 12:08: ${problematicSensorTimes.length}`);
    problematicSensorTimes.forEach(time => {
      const data = sensorData[time];
      console.log(`   ${time} - timestamp: "${data.timestamp}" - category: "${data.category}"`);
    });
    
    // Compare the patterns
    console.log('\n🔍 Pattern Analysis:');
    console.log(`Hall data pattern (5-min intervals):`);
    const hallPattern = hallTimestamps.slice(20, 30); // Sample around the middle
    hallPattern.forEach(time => {
      console.log(`   ${time}`);
    });
    
    console.log(`\nSensor_Data pattern:`);
    const sensorPattern = sensorTimestamps.slice(20, 30); // Sample around the middle
    sensorPattern.forEach(time => {
      console.log(`   ${time}`);
    });
    
    // Check for duplicates or near-duplicates
    console.log('\n🔍 Checking for timestamp clustering...');
    const timeGroups = {};
    sensorTimestamps.forEach(time => {
      const hourMin = time.substring(0, 5); // Get HH:MM part
      if (!timeGroups[hourMin]) {
        timeGroups[hourMin] = [];
      }
      timeGroups[hourMin].push(time);
    });
    
    // Find groups with multiple entries
    const clusteredTimes = Object.entries(timeGroups).filter(([_, times]) => times.length > 1);
    if (clusteredTimes.length > 0) {
      console.log('⚠️ Found timestamp clustering:');
      clusteredTimes.forEach(([hourMin, times]) => {
        console.log(`   ${hourMin}:XX - ${times.length} entries: ${times.join(', ')}`);
      });
    }
    
    // Check the source of the problem - look at recent entries
    console.log('\n🔍 Checking recent entries for timestamp source...');
    const recentSensorTimes = sensorTimestamps.slice(-10);
    recentSensorTimes.forEach(time => {
      const data = sensorData[time];
      console.log(`   ${time}:`);
      console.log(`     timestamp: "${data.timestamp}"`);
      console.log(`     createdAt: ${data.createdAt}`);
      console.log(`     category: "${data.category}"`);
      if (data.originalTimestamp) {
        console.log(`     originalTimestamp: "${data.originalTimestamp}"`);
      }
    });
    
    // Check if there are entries that don't match hall pattern
    console.log('\n🔍 Checking for entries not in hall data...');
    const hallTimeSet = new Set(hallTimestamps);
    const extraSensorTimes = sensorTimestamps.filter(time => !hallTimeSet.has(time));
    
    if (extraSensorTimes.length > 0) {
      console.log(`⚠️ Found ${extraSensorTimes.length} Sensor_Data entries not in hall data:`);
      extraSensorTimes.slice(0, 10).forEach(time => {
        const data = sensorData[time];
        console.log(`   ${time} - timestamp: "${data.timestamp}"`);
      });
      if (extraSensorTimes.length > 10) {
        console.log(`   ... and ${extraSensorTimes.length - 10} more`);
      }
    }
    
    console.log('\n📊 Summary:');
    console.log(`Hall data entries: ${hallTimestamps.length}`);
    console.log(`Sensor_Data entries: ${sensorTimestamps.length}`);
    console.log(`Extra entries in Sensor_Data: ${extraSensorTimes.length}`);
    console.log(`Timestamp clusters: ${clusteredTimes.length}`);
    
  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Handle script execution
if (require.main === module) {
  debugTimestampIssue();
}

module.exports = { debugTimestampIssue };
