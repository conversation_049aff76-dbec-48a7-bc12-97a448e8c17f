# AirSense - Complete System Startup Script (Updated with Contact Service)
# This script starts all services including the Contact Service

Write-Host "🚀 Starting AirSense Complete System..." -ForegroundColor Green
Write-Host "=========================================" -ForegroundColor Cyan

# Function to start a service in a new PowerShell window
function Start-ServiceWindow {
    param(
        [string]$ServiceName,
        [string]$Directory,
        [string]$Command,
        [int]$Port,
        [string]$Color = "White"
    )
    
    Write-Host "🔄 Starting $ServiceName (Port $Port)..." -ForegroundColor $Color
    
    $scriptBlock = @"
Set-Location '$Directory'
Write-Host '🚀 $ServiceName Starting on Port $Port...' -ForegroundColor $Color
Write-Host 'Directory: $(Get-Location)' -ForegroundColor Gray
$Command
"@
    
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $scriptBlock
    Start-Sleep -Seconds 2
}

# Start all services in order
try {
    Write-Host "`n🔧 Starting backend services..." -ForegroundColor Yellow
    
    # 1. Sensor Data Service (Port 3001) - Core Firebase service
    Start-ServiceWindow -ServiceName "Sensor Data Service" -Directory "sensor-data-service" -Command "npm run dev" -Port 3001 -Color "Blue"
    
    # 2. Location Service (Port 3002) - Location sync
    Start-ServiceWindow -ServiceName "Location Service" -Directory "location-service" -Command "npm run dev" -Port 3002 -Color "Green"
    
    # 3. Contact Service (Port 3005) - Contact form backend
    Start-ServiceWindow -ServiceName "Contact Service" -Directory "contact-service" -Command "npm run dev" -Port 3005 -Color "Magenta"
    
    Write-Host "`n🎨 Starting frontend services..." -ForegroundColor Yellow
    
    # 4. Admin Interface (Port 3004)
    Start-ServiceWindow -ServiceName "Admin Interface" -Directory "airsense-admin" -Command "npm start" -Port 3004 -Color "Yellow"
    
    # 5. Main React App (Port 5173)
    Start-ServiceWindow -ServiceName "Main React App" -Directory "airsense-react" -Command "npm run dev:main-only" -Port 5173 -Color "Cyan"
    
    Write-Host "`n✅ All services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Application URLs:" -ForegroundColor White
    Write-Host "  📱 Main App:        http://localhost:5173" -ForegroundColor Cyan
    Write-Host "  👑 Admin Panel:     http://localhost:3004" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔧 Backend Services:" -ForegroundColor White
    Write-Host "  📊 Sensor Data:     http://localhost:3001" -ForegroundColor Blue
    Write-Host "  📍 Location Sync:   http://localhost:3002" -ForegroundColor Green
    Write-Host "  📧 Contact Service: http://localhost:3005" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "🔥 Firebase Database:" -ForegroundColor Red
    Write-Host "  🗄️  Database URL:    https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/" -ForegroundColor Gray
    Write-Host ""
    Write-Host "💡 Important Notes:" -ForegroundColor Yellow
    Write-Host "  • Wait 15-20 seconds for all services to fully initialize" -ForegroundColor Gray
    Write-Host "  • Contact form will now work properly with backend validation" -ForegroundColor Gray
    Write-Host "  • All contact submissions are stored in Firebase under 'contacts'" -ForegroundColor Gray
    Write-Host "  • Check each PowerShell window for individual service status" -ForegroundColor Gray
    Write-Host "  • Close PowerShell windows to stop services" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🎯 Ready! Open http://localhost:5173 to use AirSense" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error starting services: $_" -ForegroundColor Red
    Write-Host "Please check that all directories exist and npm dependencies are installed." -ForegroundColor Yellow
    exit 1
}

# Keep this window open for monitoring
Write-Host ""
Write-Host "📊 Service Monitor - Keep this window open" -ForegroundColor Cyan
Write-Host "Press Ctrl+C to close this monitor (services will continue running)" -ForegroundColor Gray
Write-Host ""

# Simple monitoring loop
$startTime = Get-Date
while ($true) {
    Start-Sleep -Seconds 30
    $elapsed = (Get-Date) - $startTime
    $timeString = "{0:hh\:mm\:ss}" -f $elapsed
    Write-Host "⏱️  Services running for: $timeString" -ForegroundColor Gray
}
