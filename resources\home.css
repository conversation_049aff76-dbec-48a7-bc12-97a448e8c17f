* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
    background-image:"background.jpg";
    background-attachment: fixed;
    background-size: 100% 100%;
}

.nav-bar {
  background: white;
  padding: 15px 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  color:rgb(81, 72, 72);
  min-height: 80px;
  position: relative;
}
.nav-links {
  flex: 1;
  display: flex;
  justify-content: center;
  gap: 2rem;
}
.search-container {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 1rem;
}
.home-icon-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0.4rem;
}
.home-icon{
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
}

.logo-container {
  flex: 1;
  display: flex;
  align-items: center;
}

.logo-wrapper {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logo {
  height: 80px;
  border-radius: 10px
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: var(--text-secondary);
  font-weight: 500;
  padding: 0.75rem 1.8rem;
  border-radius: 6px;
  transition: all 0.3s ease;
  font-size: 1.4rem;
}

.nav-links a.active,
.nav-links a:hover {
  color: var(--primary-color);
  background: rgba(79, 184, 163, 0.1);
}

.auth-buttons {
  display: flex;
  gap: 1rem;
}


.user-info-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /*padding: 1rem 2rem;*/
  background-color: #dbdddf;
  border-bottom: 1px solid #dee2e6;
}

.hero {
    text-align: center;
    padding: 50px 20px;
    background: url('background.jpg') no-repeat center center/cover;
    color: #333;
}

.hero h1 {
    font-size: 50px;
    margin-bottom: 10px;
}

.hero h2 {
    font-size: 45px;
    
    font-weight: bold;
    color: #090909;
}

.hero p {
    font-size: 25px;
    margin-bottom: 20px;
}

.real-time-monitoring {
    margin: 20px auto;
    padding: 15px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    max-width: 400px;
    border-radius: 8px;
}

footer {
    text-align: center;
    padding: 20px 0;
    background-color: #f1f1f1;
    color: #555;
    font-size: 16px;
}
.logo {
    height: 80px;
  /*margin: 0;*/
  }
  .features {
    background-color: #dfdede;
    padding: 2rem 1rem;
  }
  
  .features h2 {
    font-family: 'Poppins', sans-serif;
    font-size:40px;
    text-align: center;
    margin-bottom: 1.5rem;
  }
  
  .feature-item {
    background-color: #F7F7F7;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  }
  
  .feature-item h3 {
    font-weight:bold ;
    font-family: 'Poppins', sans-serif;
    font-size: 25px;
    color: #2A3E59;
    margin-bottom: 0.5rem;
  }
  
  .feature-item p {
    font-size: 20px;
    color: #555555;
  }
  
  .carousel-indicators button {
    background-color: #32B3A4;
  }
  
  .carousel-control-prev-icon,
  .carousel-control-next-icon {
    background-color: #32B3A4;
    border-radius: 50%;
    width: 30px;
    height: 30px;
  }
  .footer {
    background-color: #2c3e50;
    color: white;
    padding: 1.5rem;
    text-align: center;
  }
  .footer-links {
    margin: 1rem 0;
  }
  .footer-links a {
    color: #bbbebd;
    text-decoration: none;
    margin: 0 1rem;
    font-weight: bold;
  }
  .footer-links a:hover {
    text-decoration: underline;
  }
  .footer-social-icons {
    margin-top: 1rem;
  }
  .footer-social-icons img {
    height: 50px;
    width: 50px;
    margin: 0 0.5rem;
    vertical-align: middle;
    border-radius: 10px;
    gap: 300px;
  }

.container {
  background-color: #FFFFFF;
  padding: 2rem;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  width: 400px;
  max-width: 90%;
  text-align: center;
}

.input-group {
  margin-bottom: 1.5rem;
  text-align: left;
}

.input-group label {
  display: block;
  font-weight: 500;
  color: #2A3E59;
  margin-bottom: 0.5rem;
}

.input-group input, .input-group select {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #32B3A4;
  border-radius: 5px;
  font-size: 1rem;
}

.input-group input:focus, .input-group select:focus {
  outline: none;
  border-color: #27988A;
  box-shadow: 0 0 5px rgba(50, 179, 164, 0.5);
}

.btn-primary {
  display: inline-block;
  background-color: #32B3A4;
  color: #FFFFFF;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.btn-primary:hover {
  background-color: #27988A;
}

.hidden {
  display: none;
}

.view {
  display: block;
}
