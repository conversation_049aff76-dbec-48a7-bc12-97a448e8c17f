const { FirebaseService } = require('./firebase');
const AdminSessionService = require('./admin-session-service');
const DelayedStorageService = require('./delayed-storage-service');

/**
 * Location-Based Data Collection Controller
 * Handles automatic sensor data storage with admin-selected locations
 */
class LocationDataController {
  constructor() {
    this.adminSessionService = new AdminSessionService();
    this.delayedStorageService = new DelayedStorageService(0); 
    this.lastKnownLocation = null;
    this.dataCollectionActive = false;
    this.deviceId = 'esp32-main'; // Can be configured per device
  }

  /**
   * Initialize the controller
   */
  async initialize() {
    try {
      console.log('🚀 Initializing Location Data Controller...');
      
      // Check if there's an active admin session and location
      const sessionStatus = await this.adminSessionService.getSessionStatus();
      
      if (sessionStatus.adminSession.isLoggedIn && sessionStatus.activeLocation.location) {
        this.lastKnownLocation = sessionStatus.activeLocation.location;
        this.dataCollectionActive = sessionStatus.dataCollection.isActive;
        console.log(`📍 Found active session - Location: ${this.lastKnownLocation}, Collection: ${this.dataCollectionActive ? 'Active' : 'Inactive'}`);
      } else {
        console.log('📍 No active admin session found');
      }

      // Start session monitoring
      this.adminSessionService.startSessionMonitoring();

      // Start delayed storage service
      this.delayedStorageService.start();

      console.log('✅ Location Data Controller initialized');
      return true;
    } catch (error) {
      console.error('❌ Failed to initialize Location Data Controller:', error);
      throw error;
    }
  }

  /**
   * Process incoming sensor data from ESP32
   */
  async processSensorData(sensorData, deviceLocation = null) {
    try {
      console.log('📊 Processing sensor data...');
      
      // Check if data collection is active
      const shouldCollect = await this.adminSessionService.shouldCollectData();
      
      if (!shouldCollect) {
        console.log('⚠️ Data collection not active - Admin not logged in or no location selected');
        return {
          success: false,
          reason: 'Data collection not active',
          timestamp: new Date().toISOString()
        };
      }

      // Get the current active location - only use admin-selected location
      let targetLocation = await this.adminSessionService.getActiveLocationForStorage();
      
      if (!targetLocation) {
        console.log('❌ No target location available for data storage');
        return {
          success: false,
          reason: 'No active location set',
          timestamp: new Date().toISOString()
        };
      }

      // Calculate AQI (Air Quality Index)
      const aqi = this.calculateAQI(sensorData);

      // Enhance sensor data with metadata - no deviceLocation
      const enhancedData = {
        ...sensorData,
        aqi: aqi,
        deviceId: this.deviceId,
        processedAt: new Date().toISOString()
      };

      // Queue data for delayed storage (5-minute delay)
      const queueResult = await this.delayedStorageService.queueSensorData(
        enhancedData,
        targetLocation,
        this.deviceId
      );

      if (queueResult.success) {
        console.log(`⏰ Sensor data queued for delayed storage (${queueResult.delayMinutes} min delay)`);
        this.lastKnownLocation = targetLocation;

        // Return the exact structure requested
        const responseData = {
          success: true,
          sensorData: {
            aqi: aqi,
            co2: sensorData.co2 || 0,
            createdAt: Date.now(),
            delayMinutes: queueResult.delayMinutes,
            delayedStorage: true,
            deviceId: this.deviceId,
            humidity: sensorData.humidity || 0,
            location: targetLocation,
            pm10: sensorData.pm10 || 0,
            pm25: sensorData.pm25 || 0,
            processedAt: new Date().toISOString(),
            receivedAt: new Date().toISOString(),
            storedAt: queueResult.willStoreAt,
            temperature: sensorData.temperature || 0,
            gasResistance: sensorData.gasResistance || 0,  // Fixed: use gasResistance instead of voc
            pressure: sensorData.pressure || 0
          },
          location: targetLocation,
          timestamp: new Date().toISOString(),
          aqi: aqi,
          deviceId: this.deviceId,
          delayedStorage: true,
          willStoreAt: queueResult.willStoreAt,
          delayMinutes: queueResult.delayMinutes,
          queueSize: queueResult.queueSize || 0
        };

        return responseData;
      } else {
        return {
          success: false,
          reason: 'Failed to queue data for delayed storage',
          timestamp: new Date().toISOString()
        };
      }

    } catch (error) {
      console.error('❌ Error processing sensor data:', error);
      return {
        success: false,
        reason: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Handle admin login event
   */
  async handleAdminLogin(email, password) {
    try {
      const result = await this.adminSessionService.handleAdminLogin(email, password);
      console.log('👑 Admin login processed by Location Data Controller');
      return result;
    } catch (error) {
      console.error('❌ Error handling admin login:', error);
      throw error;
    }
  }

  /**
   * Handle admin logout event
   */
  async handleAdminLogout(email) {
    try {
      const result = await this.adminSessionService.handleAdminLogout(email);
      
      // Clear local state
      this.lastKnownLocation = null;
      this.dataCollectionActive = false;
      
      console.log('👑 Admin logout processed - Data collection stopped');
      return result;
    } catch (error) {
      console.error('❌ Error handling admin logout:', error);
      throw error;
    }
  }

  /**
   * Handle location selection by admin
   */
  async handleLocationSelection(location, adminEmail) {
    try {
      const result = await this.adminSessionService.handleLocationSelection(location, adminEmail);
      
      // Update local state
      this.lastKnownLocation = location;
      this.dataCollectionActive = true;
      
      console.log(`📍 Location selection processed - Data collection started for: ${location}`);
      return result;
    } catch (error) {
      console.error('❌ Error handling location selection:', error);
      throw error;
    }
  }

  /**
   * Get current controller status
   */
  async getStatus() {
    try {
      const sessionStatus = await this.adminSessionService.getSessionStatus();
      const queueStatus = this.delayedStorageService.getQueueStatus();

      return {
        controller: {
          lastKnownLocation: this.lastKnownLocation,
          dataCollectionActive: this.dataCollectionActive,
          deviceId: this.deviceId
        },
        session: sessionStatus,
        delayedStorage: queueStatus,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting controller status:', error);
      throw error;
    }
  }

  /**
   * Calculate Air Quality Index (AQI) from sensor data
   */
  calculateAQI(sensorData) {
    try {
      // Simple AQI calculation based on PM2.5 (can be enhanced)
      const pm25 = sensorData.pm25 || 0;
      
      let aqi;
      if (pm25 <= 12) {
        aqi = Math.round((50 / 12) * pm25);
      } else if (pm25 <= 35.4) {
        aqi = Math.round(((100 - 51) / (35.4 - 12.1)) * (pm25 - 12.1) + 51);
      } else if (pm25 <= 55.4) {
        aqi = Math.round(((150 - 101) / (55.4 - 35.5)) * (pm25 - 35.5) + 101);
      } else if (pm25 <= 150.4) {
        aqi = Math.round(((200 - 151) / (150.4 - 55.5)) * (pm25 - 55.5) + 151);
      } else if (pm25 <= 250.4) {
        aqi = Math.round(((300 - 201) / (250.4 - 150.5)) * (pm25 - 150.5) + 201);
      } else {
        aqi = Math.round(((500 - 301) / (500.4 - 250.5)) * (pm25 - 250.5) + 301);
      }
      
      return Math.max(0, Math.min(500, aqi));
    } catch (error) {
      console.error('❌ Error calculating AQI:', error);
      return 0;
    }
  }

  /**
   * Handle device restart
   */
  async handleDeviceRestart() {
    try {
      console.log('📱 Handling device restart...');
      
      const lastLocation = await this.adminSessionService.handleDeviceRestart();
      
      if (lastLocation) {
        this.lastKnownLocation = lastLocation;
        this.dataCollectionActive = true;
        console.log(`📱 Device restart handled - Using location: ${lastLocation}`);
      } else {
        this.lastKnownLocation = null;
        this.dataCollectionActive = false;
        console.log('📱 Device restart handled - No active location');
      }
      
      return {
        success: true,
        location: this.lastKnownLocation,
        dataCollectionActive: this.dataCollectionActive
      };
    } catch (error) {
      console.error('❌ Error handling device restart:', error);
      throw error;
    }
  }

  /**
   * Get delayed storage queue status
   */
  getDelayedStorageStatus() {
    return this.delayedStorageService.getQueueStatus();
  }

  /**
   * Get pending delayed storage data (for debugging)
   */
  getPendingDelayedData() {
    return this.delayedStorageService.getPendingData();
  }

  /**
   * Clear delayed storage queue (use with caution)
   */
  clearDelayedStorageQueue() {
    return this.delayedStorageService.clearQueue();
  }

  /**
   * Cleanup and shutdown
   */
  shutdown() {
    console.log('🛑 Shutting down Location Data Controller...');
    this.adminSessionService.stopSessionMonitoring();
    this.delayedStorageService.stop();
    console.log('✅ Location Data Controller shutdown complete');
  }
}

module.exports = LocationDataController;






