#!/usr/bin/env node

/**
 * Test Sensor Data Flow with Sensor_Data Category Storage
 * 
 * This script tests the complete data flow:
 * 1. Set admin location and Sensor_Data category
 * 2. Send sensor data to simulate ESP32
 * 3. Verify data is stored in both locations
 * 4. Check the hierarchical structure in Sensor_Data
 * 
 * Usage: node test-sensor-data-flow.js
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3003';

async function testSensorDataFlow() {
  console.log('🧪 Starting Sensor Data Flow Test...\n');
  
  try {
    // Step 1: Set admin location
    console.log('📍 Step 1: Setting admin location...');
    const locationResponse = await axios.post(`${BASE_URL}/api/admin/location`, {
      location: 'basement',
      adminEmail: '<EMAIL>'
    });
    
    if (locationResponse.data.success) {
      console.log('✅ Location set:', locationResponse.data.location);
    } else {
      throw new Error('Failed to set location');
    }
    
    // Step 2: Set Sensor_Data category
    console.log('\n📁 Step 2: Setting Sensor_Data category...');
    const categoryResponse = await axios.post(`${BASE_URL}/api/admin/sensor-data-category`, {
      category: 'Basement',
      adminEmail: '<EMAIL>'
    });
    
    if (categoryResponse.data.success) {
      console.log('✅ Sensor_Data category set:', categoryResponse.data.category);
    } else {
      throw new Error('Failed to set Sensor_Data category');
    }
    
    // Step 3: Send test sensor data
    console.log('\n📊 Step 3: Sending test sensor data...');
    const sensorData = {
      temperature: 30.5,
      humidity: 70.1,
      co2: 442,
      pm25: 4,
      pm10: 9,
      gasResistance: 14.92,
      pressure: 1008.46002
    };
    
    const dataResponse = await axios.post(`${BASE_URL}/api/sensor-data`, sensorData);
    
    if (dataResponse.data.success) {
      console.log('✅ Sensor data sent successfully');
      console.log(`   📍 Location: ${dataResponse.data.location}`);
      console.log(`   📊 AQI: ${dataResponse.data.aqi}`);
      console.log(`   ⏰ Delayed storage: ${dataResponse.data.delayedStorage ? 'Yes' : 'No'}`);
      if (dataResponse.data.delayedStorage) {
        console.log(`   ⏳ Will store at: ${dataResponse.data.willStoreAt}`);
      }
    } else {
      throw new Error('Failed to send sensor data');
    }
    
    // Step 4: Wait for delayed storage (if applicable)
    if (dataResponse.data.delayedStorage) {
      console.log('\n⏳ Step 4: Waiting for delayed storage...');
      console.log('   (In production, this would be handled automatically)');
      console.log('   (For testing, data should appear in both locations after delay)');
    }
    
    // Step 5: Verify data in original location
    console.log('\n🔍 Step 5: Checking data in original location (sensorData/basement)...');
    const originalDataResponse = await axios.get(`${BASE_URL}/api/sensor-data/basement/history?limit=1`);

    if (originalDataResponse.data.success && originalDataResponse.data.count > 0) {
      console.log('✅ Data found in original location');
      console.log(`   📊 Count: ${originalDataResponse.data.count}`);
      const latestData = originalDataResponse.data.data[0];
      console.log(`   🌡️ Temperature: ${latestData.temperature}°C`);
      console.log(`   💧 Humidity: ${latestData.humidity}%`);
    } else {
      console.log('⚠️ No data found in original location (may be delayed)');
    }
    
    // Step 6: Verify data in Sensor_Data category
    console.log('\n🔍 Step 6: Checking data in Sensor_Data/Basement...');
    const categoryDataResponse = await axios.get(`${BASE_URL}/api/sensor-data-section/Basement?limit=1`);
    
    if (categoryDataResponse.data.success && categoryDataResponse.data.count > 0) {
      console.log('✅ Data found in Sensor_Data category');
      console.log(`   📊 Count: ${categoryDataResponse.data.count}`);
      const latestCategoryData = categoryDataResponse.data.data[0];
      console.log(`   🌡️ Temperature: ${latestCategoryData.temperature}°C`);
      console.log(`   💧 Humidity: ${latestCategoryData.humidity}%`);
      console.log(`   📁 Category: ${latestCategoryData.category}`);
    } else {
      console.log('⚠️ No data found in Sensor_Data category (may be delayed)');
    }
    
    // Step 7: Check active settings
    console.log('\n⚙️ Step 7: Verifying active settings...');
    const activeCategoryResponse = await axios.get(`${BASE_URL}/api/admin/sensor-data-category`);
    
    if (activeCategoryResponse.data.success) {
      const activeCategory = activeCategoryResponse.data.activeCategory;
      console.log('✅ Active Sensor_Data category:', activeCategory.category);
      console.log(`   👤 Set by: ${activeCategory.setBy}`);
      console.log(`   📅 Set at: ${activeCategory.setAt}`);
    }
    
    console.log('\n🎉 Sensor Data Flow Test Completed!');
    console.log('\n📋 Summary:');
    console.log('✅ Admin location set successfully');
    console.log('✅ Sensor_Data category set successfully');
    console.log('✅ Sensor data sent successfully');
    console.log('✅ Data flow configured for dual storage');
    
    console.log('\n📍 Expected Behavior:');
    console.log('1. Real ESP32 data from "hall" will be stored in selected location (basement)');
    console.log('2. Same data will also be stored in Sensor_Data/Basement with date/time hierarchy');
    console.log('3. Structure: Sensor_Data/Basement/2025-07-20/00:11:09/');
    console.log('4. Data includes: co2, gasResistance, humidity, pm10, pm25, pressure, temperature, timestamp');
    
    console.log('\n🔗 Firebase Database URL: https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/');
    console.log('📍 Check paths:');
    console.log('   - sensorData/basement/ (original location)');
    console.log('   - Sensor_Data/Basement/ (hierarchical structure)');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
    if (error.response) {
      console.error('   Response:', error.response.data);
    }
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  testSensorDataFlow();
}

module.exports = { testSensorDataFlow };
