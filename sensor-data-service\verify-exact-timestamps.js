#!/usr/bin/env node

/**
 * Verify Exact Timestamps in Sensor_Data
 * 
 * This script verifies that the Sensor_Data structure now contains
 * the exact same timestamps as the hall data.
 */

const { getDatabase } = require('./firebase');

async function verifyExactTimestamps() {
  console.log('🔍 Verifying Exact Timestamps in Sensor_Data...\n');
  
  try {
    const db = getDatabase();
    const targetDate = '2025-07-20';
    const category = 'Basement';
    
    // Get hall data timestamps
    console.log('📊 Fetching hall data timestamps...');
    const hallRef = db.ref(`hall/data/${targetDate}`);
    const hallSnapshot = await hallRef.once('value');
    
    if (!hallSnapshot.exists()) {
      console.log('❌ No hall data found');
      return;
    }
    
    const hallData = hallSnapshot.val();
    const hallTimestamps = Object.keys(hallData).sort();
    console.log(`✅ Found ${hallTimestamps.length} hall timestamps`);
    
    // Get Sensor_Data timestamps
    console.log('\n📊 Fetching Sensor_Data timestamps...');
    const sensorDataRef = db.ref(`Sensor_Data/${category}/${targetDate}`);
    const sensorDataSnapshot = await sensorDataRef.once('value');
    
    if (!sensorDataSnapshot.exists()) {
      console.log('❌ No Sensor_Data found');
      return;
    }
    
    const sensorData = sensorDataSnapshot.val();
    const sensorTimestamps = Object.keys(sensorData).sort();
    console.log(`✅ Found ${sensorTimestamps.length} Sensor_Data timestamps`);
    
    // Compare timestamps
    console.log('\n🔍 Comparing timestamps...');
    
    const missingInSensorData = hallTimestamps.filter(t => !sensorTimestamps.includes(t));
    const extraInSensorData = sensorTimestamps.filter(t => !hallTimestamps.includes(t));
    const matching = hallTimestamps.filter(t => sensorTimestamps.includes(t));
    
    console.log(`✅ Matching timestamps: ${matching.length}`);
    console.log(`⚠️ Missing in Sensor_Data: ${missingInSensorData.length}`);
    console.log(`⚠️ Extra in Sensor_Data: ${extraInSensorData.length}`);
    
    if (missingInSensorData.length > 0) {
      console.log('\n❌ Missing timestamps:');
      missingInSensorData.slice(0, 5).forEach(t => console.log(`   ${t}`));
      if (missingInSensorData.length > 5) {
        console.log(`   ... and ${missingInSensorData.length - 5} more`);
      }
    }
    
    if (extraInSensorData.length > 0) {
      console.log('\n⚠️ Extra timestamps:');
      extraInSensorData.slice(0, 5).forEach(t => console.log(`   ${t}`));
      if (extraInSensorData.length > 5) {
        console.log(`   ... and ${extraInSensorData.length - 5} more`);
      }
    }
    
    // Show sample data comparison
    if (matching.length > 0) {
      console.log('\n📊 Sample data comparison:');
      const sampleTime = matching[0];
      
      const hallEntry = hallData[sampleTime];
      const sensorEntry = sensorData[sampleTime];
      
      console.log(`\n🏛️ Hall data (${sampleTime}):`);
      console.log(`   🌡️ Temperature: ${hallEntry.temperature}°C`);
      console.log(`   💧 Humidity: ${hallEntry.humidity}%`);
      console.log(`   🌬️ CO2: ${hallEntry.co2} ppm`);
      console.log(`   ⏰ Timestamp: ${hallEntry.timestamp}`);
      
      console.log(`\n📁 Sensor_Data (${sampleTime}):`);
      console.log(`   🌡️ Temperature: ${sensorEntry.temperature}°C`);
      console.log(`   💧 Humidity: ${sensorEntry.humidity}%`);
      console.log(`   🌬️ CO2: ${sensorEntry.co2} ppm`);
      console.log(`   ⏰ Timestamp: ${sensorEntry.timestamp}`);
      
      // Check if data matches
      const dataMatches = (
        hallEntry.temperature === sensorEntry.temperature &&
        hallEntry.humidity === sensorEntry.humidity &&
        hallEntry.co2 === sensorEntry.co2 &&
        hallEntry.timestamp === sensorEntry.timestamp
      );
      
      console.log(`\n${dataMatches ? '✅' : '❌'} Data content matches: ${dataMatches}`);
    }
    
    // Show structure verification
    console.log('\n🏗️ Structure verification:');
    console.log(`📁 Path: Sensor_Data/${category}/${targetDate}/`);
    console.log('📊 Sample structure:');
    
    if (matching.length > 0) {
      const sampleTimes = matching.slice(0, 3);
      sampleTimes.forEach(time => {
        console.log(`   └── ${time}/`);
        console.log(`       ├── co2: ${sensorData[time].co2}`);
        console.log(`       ├── gasResistance: ${sensorData[time].gasResistance}`);
        console.log(`       ├── humidity: ${sensorData[time].humidity}`);
        console.log(`       ├── pm10: ${sensorData[time].pm10}`);
        console.log(`       ├── pm25: ${sensorData[time].pm25}`);
        console.log(`       ├── pressure: ${sensorData[time].pressure}`);
        console.log(`       ├── temperature: ${sensorData[time].temperature}`);
        console.log(`       └── timestamp: "${sensorData[time].timestamp}"`);
      });
    }
    
    console.log('\n🎉 Verification completed!');
    
    if (matching.length === hallTimestamps.length && missingInSensorData.length === 0) {
      console.log('✅ Perfect match! All hall timestamps are present in Sensor_Data with exact timing.');
    } else {
      console.log('⚠️ Some discrepancies found. Check the details above.');
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error);
  }
}

// Handle script execution
if (require.main === module) {
  verifyExactTimestamps();
}

module.exports = { verifyExactTimestamps };
