/**
 * Contact Form Validation Utilities
 * Provides comprehensive validation for contact form submissions
 */

// Email validation regex (RFC 5322 compliant, stricter version)
const EMAIL_REGEX = /^[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-zA-Z0-9!#$%&'*+/=?^_`{|}~-]+)*@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;

// Field length constraints
const FIELD_CONSTRAINTS = {
  name: {
    min: 2,
    max: 100
  },
  subject: {
    min: 5,
    max: 200
  },
  message: {
    min: 10,
    max: 2000
  }
};

/**
 * Validates email format
 * @param {string} email - Email address to validate
 * @returns {boolean} - True if valid email format
 */
function isValidEmail(email) {
  if (!email || typeof email !== 'string') {
    return false;
  }
  return EMAIL_REGEX.test(email.trim());
}

/**
 * Validates a string field with length constraints
 * @param {string} value - Value to validate
 * @param {string} fieldName - Name of the field for error messages
 * @param {Object} constraints - Min/max length constraints
 * @returns {string|null} - Error message or null if valid
 */
function validateStringField(value, fieldName, constraints) {
  if (!value || typeof value !== 'string') {
    return `${fieldName} is required and must be a non-empty string`;
  }
  
  const trimmedValue = value.trim();
  
  if (trimmedValue.length === 0) {
    return `${fieldName} cannot be empty`;
  }
  
  if (trimmedValue.length < constraints.min) {
    return `${fieldName} must be at least ${constraints.min} characters long`;
  }
  
  if (trimmedValue.length > constraints.max) {
    return `${fieldName} must be less than ${constraints.max} characters`;
  }
  
  return null;
}

/**
 * Validates name field
 * @param {string} name - Name to validate
 * @returns {string|null} - Error message or null if valid
 */
function validateName(name) {
  const error = validateStringField(name, 'Name', FIELD_CONSTRAINTS.name);
  if (error) return error;
  
  // Additional name-specific validations
  const trimmedName = name.trim();
  
  // Check for invalid characters (only letters, spaces, hyphens, apostrophes)
  const nameRegex = /^[a-zA-Z\s\-']+$/;
  if (!nameRegex.test(trimmedName)) {
    return 'Name can only contain letters, spaces, hyphens, and apostrophes';
  }
  
  return null;
}

/**
 * Validates email field
 * @param {string} email - Email to validate
 * @returns {string|null} - Error message or null if valid
 */
function validateEmail(email) {
  if (!email || typeof email !== 'string') {
    return 'Email is required and must be a non-empty string';
  }
  
  const trimmedEmail = email.trim();
  
  if (trimmedEmail.length === 0) {
    return 'Email cannot be empty';
  }
  
  if (!isValidEmail(trimmedEmail)) {
    return 'Email must be in a valid format (e.g., <EMAIL>)';
  }
  
  if (trimmedEmail.length > 254) {
    return 'Email address is too long';
  }
  
  return null;
}

/**
 * Validates subject field
 * @param {string} subject - Subject to validate
 * @returns {string|null} - Error message or null if valid
 */
function validateSubject(subject) {
  return validateStringField(subject, 'Subject', FIELD_CONSTRAINTS.subject);
}

/**
 * Validates message field
 * @param {string} message - Message to validate
 * @returns {string|null} - Error message or null if valid
 */
function validateMessage(message) {
  return validateStringField(message, 'Message', FIELD_CONSTRAINTS.message);
}

/**
 * Validates entire contact form data
 * @param {Object} data - Contact form data
 * @returns {Array} - Array of validation error messages
 */
function validateContactForm(data) {
  const errors = [];
  
  // Validate each field
  const nameError = validateName(data.name);
  if (nameError) errors.push(nameError);
  
  const emailError = validateEmail(data.email);
  if (emailError) errors.push(emailError);
  
  const subjectError = validateSubject(data.subject);
  if (subjectError) errors.push(subjectError);
  
  const messageError = validateMessage(data.message);
  if (messageError) errors.push(messageError);
  
  return errors;
}

/**
 * Sanitizes contact form data
 * @param {Object} data - Raw contact form data
 * @returns {Object} - Sanitized contact form data
 */
function sanitizeContactData(data) {
  return {
    name: data.name ? data.name.trim() : '',
    email: data.email ? data.email.trim().toLowerCase() : '',
    subject: data.subject ? data.subject.trim() : '',
    message: data.message ? data.message.trim() : ''
  };
}

/**
 * Checks if contact form data is complete
 * @param {Object} data - Contact form data
 * @returns {boolean} - True if all required fields are present
 */
function isCompleteContactData(data) {
  return !!(data.name && data.email && data.subject && data.message);
}

module.exports = {
  isValidEmail,
  validateName,
  validateEmail,
  validateSubject,
  validateMessage,
  validateContactForm,
  sanitizeContactData,
  isCompleteContactData,
  FIELD_CONSTRAINTS
};
