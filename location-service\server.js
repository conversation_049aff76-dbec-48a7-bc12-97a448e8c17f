const express = require('express');
const cors = require('cors');
const WebSocket = require('ws');
const fs = require('fs-extra');
const path = require('path');
const http = require('http');

const app = express();
const PORT = process.env.PORT || 3002;

// Middleware
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:3001',
    'http://localhost:3003',
    'http://localhost:3004'
  ],
  credentials: true
}));
app.use(express.json());

// Request logging middleware
app.use((req, res, next) => {
  const timestamp = new Date().toISOString();
  const clientInfo = req.ip || req.connection.remoteAddress || 'unknown';
  console.log(`🌐 ${timestamp} - ${req.method} ${req.path} from ${clientInfo}`);
  next();
});

// Data storage file
const DATA_FILE = path.join(__dirname, 'location-data.json');

// Default location data - simplified to just currentLocation
const DEFAULT_DATA = {
  currentLocation: '',
  lastUpdated: null,
  updatedBy: null
};

// Initialize data file if it doesn't exist
async function initializeData() {
  try {
    if (!await fs.pathExists(DATA_FILE)) {
      await fs.writeJson(DATA_FILE, DEFAULT_DATA, { spaces: 2 });
      console.log('📁 Initialized location data file');
    }
  } catch (error) {
    console.error('❌ Error initializing data file:', error);
  }
}

// Read location data
async function readLocationData() {
  try {
    return await fs.readJson(DATA_FILE);
  } catch (error) {
    console.error('❌ Error reading location data:', error);
    return DEFAULT_DATA;
  }
}

// Write location data
async function writeLocationData(data) {
  try {
    await fs.writeJson(DATA_FILE, data, { spaces: 2 });
    return true;
  } catch (error) {
    console.error('❌ Error writing location data:', error);
    return false;
  }
}

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server
const wss = new WebSocket.Server({ server });

// Store connected clients
const clients = new Set();

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  const clientId = `${req.socket.remoteAddress}:${req.socket.remotePort}`;
  console.log('🔌 New WebSocket connection from:', clientId);
  clients.add(ws);
  console.log(`📊 Total connected clients: ${clients.size}`);

  // Send current location to new client immediately
  readLocationData().then(data => {
    const message = JSON.stringify({
      type: 'location-update',
      data: data
    });
    ws.send(message);
    console.log(`📤 Sent current location to new client ${clientId}:`, data.currentLocation || 'none');
  }).catch(error => {
    console.error('❌ Error sending initial location to client:', error);
  });

  // Handle client disconnect
  ws.on('close', () => {
    console.log('🔌 WebSocket connection closed from:', clientId);
    clients.delete(ws);
    console.log(`📊 Remaining connected clients: ${clients.size}`);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error('❌ WebSocket error from', clientId, ':', error);
    clients.delete(ws);
    console.log(`📊 Remaining connected clients after error: ${clients.size}`);
  });
});

// Broadcast to all connected clients
function broadcastLocationUpdate(locationData) {
  const message = JSON.stringify({
    type: 'location-update',
    data: locationData
  });

  let successCount = 0;
  let failureCount = 0;

  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(message);
        successCount++;
      } catch (error) {
        console.error('❌ Error sending to client:', error);
        clients.delete(client);
        failureCount++;
      }
    } else {
      // Remove clients that are not in OPEN state
      clients.delete(client);
      failureCount++;
    }
  });

  console.log(`📡 Broadcasted location update "${locationData.currentLocation || 'none'}" to ${successCount} clients (${failureCount} failed/removed)`);
  console.log(`📊 Active WebSocket connections: ${clients.size}`);
}

// API Routes

// Health check
app.get('/health', (req, res) => {
  res.json({ 
    status: 'healthy', 
    service: 'airsense-location-service',
    timestamp: new Date().toISOString(),
    connectedClients: clients.size
  });
});

// Get current location
app.get('/api/location', async (req, res) => {
  try {
    const clientInfo = `${req.ip || req.connection.remoteAddress}`;
    const data = await readLocationData();
    console.log(`📍 Location requested by ${clientInfo}: "${data.currentLocation || 'none'}"`);
    res.json({
      success: true,
      location: data.currentLocation,
      lastUpdated: data.lastUpdated,
      updatedBy: data.updatedBy
    });
  } catch (error) {
    console.error('❌ Error getting location:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get location'
    });
  }
});

// Set new location (admin only)
app.post('/api/location', async (req, res) => {
  try {
    const { location, isAdmin, adminId } = req.body;
    console.log(`🔄 Location update request received: "${location || 'none'}" from ${adminId || 'admin'}`);

    // Validate admin access
    if (!isAdmin) {
      console.log('❌ Location update rejected: Admin access required');
      return res.status(403).json({
        success: false,
        error: 'Admin access required'
      });
    }

    // Validate location
    const validLocations = ['deans-office', 'lecture-hall', 'basement'];
    if (location && !validLocations.includes(location)) {
      console.log(`❌ Location update rejected: Invalid location "${location}"`);
      return res.status(400).json({
        success: false,
        error: 'Invalid location'
      });
    }

    // Get current location for comparison
    const currentData = await readLocationData();
    const previousLocation = currentData.currentLocation;

    // Update location data - only storing the admin-selected location
    const newData = {
      currentLocation: location || '',
      lastUpdated: new Date().toISOString(),
      updatedBy: adminId || 'admin'
    };

    const success = await writeLocationData(newData);

    if (success) {
      console.log(`✅ Location successfully updated from "${previousLocation || 'none'}" to "${location || 'none'}" by ${adminId || 'admin'}`);

      // Broadcast to all connected clients
      broadcastLocationUpdate(newData);

      res.json({
        success: true,
        location: newData.currentLocation,
        lastUpdated: newData.lastUpdated,
        updatedBy: newData.updatedBy
      });
    } else {
      console.log('❌ Failed to write location data to file');
      res.status(500).json({
        success: false,
        error: 'Failed to update location'
      });
    }
  } catch (error) {
    console.error('❌ Error setting location:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set location'
    });
  }
});

// Start server
async function startServer() {
  await initializeData();

  server.listen(PORT, () => {
    console.log('🚀 AirSense Location Service started');
    console.log(`📡 HTTP API: http://localhost:${PORT}`);
    console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
    console.log(`💾 Data file: ${DATA_FILE}`);
    console.log('✅ Ready for multi-machine location synchronization');
    console.log('');
    console.log('📋 Available endpoints:');
    console.log('   GET  /health - Service health check');
    console.log('   GET  /api/location - Get current location');
    console.log('   POST /api/location - Set new location (admin only)');
    console.log('');
  });
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down location service...');
  server.close(() => {
    console.log('✅ Location service stopped');
    process.exit(0);
  });
});

// Start the server
startServer().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
