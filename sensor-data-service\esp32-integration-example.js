/**
 * ESP32 Integration Example
 * Demonstrates how ESP32 devices should interact with the new location-aware database system
 */

const axios = require('axios');

class ESP32Simulator {
  constructor(deviceId = 'esp32-main', serverUrl = 'http://localhost:3003') {
    this.deviceId = deviceId;
    this.serverUrl = serverUrl;
    this.lastKnownLocation = null;
    this.isRunning = false;
    this.dataInterval = null;
  }

  /**
   * Simulate device startup/restart
   */
  async startup() {
    console.log(`📱 ESP32 Device ${this.deviceId} starting up...`);
    
    try {
      // Call device restart endpoint to get last known location
      const response = await axios.post(`${this.serverUrl}/api/device/restart`);
      
      if (response.data.success && response.data.location) {
        this.lastKnownLocation = response.data.location;
        console.log(`📍 Device restart: Using last active location: ${this.lastKnownLocation}`);
      } else {
        console.log('📍 Device restart: No active location found');
      }
      
      this.isRunning = true;
      console.log(`✅ ESP32 Device ${this.deviceId} started successfully`);
      
    } catch (error) {
      console.error('❌ Device startup error:', error.message);
      this.isRunning = true; // Continue running even if service is unavailable
    }
  }

  /**
   * Generate simulated sensor data
   */
  generateSensorData() {
    return {
      temperature: 20 + Math.random() * 15, // 20-35°C
      humidity: 30 + Math.random() * 40,    // 30-70%
      co2: 400 + Math.random() * 600,       // 400-1000 ppm
      pm25: Math.random() * 50,             // 0-50 µg/m³
      pm10: Math.random() * 80,             // 0-80 µg/m³
      gasResistance: 100 + Math.random() * 400,  // 100-500 Ω (Fixed: use gasResistance instead of voc)
      deviceId: this.deviceId,
      deviceLocation: this.lastKnownLocation // Include device's last known location
      // No timestamp here - let the server generate it
    };
  }

  /**
   * Send sensor data to the server
   */
  async sendSensorData() {
    if (!this.isRunning) return;

    try {
      const sensorData = this.generateSensorData();
      
      // Use the new location-aware endpoint
      const response = await axios.post(`${this.serverUrl}/api/sensor-data`, sensorData);
      
      if (response.data.success) {
        if (response.data.delayedStorage) {
          console.log(`⏰ Data queued for delayed storage (${response.data.delayMinutes} min delay)`);
          console.log(`   📍 Location: ${response.data.location} (AQI: ${response.data.aqi})`);
          console.log(`   ⏳ Will store at: ${response.data.willStoreAt}`);
        } else {
          console.log(`📊 Data sent successfully to location: ${response.data.location} (AQI: ${response.data.aqi})`);
        }

        // Update last known location if it changed
        if (response.data.location !== this.lastKnownLocation) {
          this.lastKnownLocation = response.data.location;
          console.log(`📍 Location updated to: ${this.lastKnownLocation}`);
        }
      } else {
        console.log(`⚠️ Data not stored: ${response.data.message}`);
      }
      
    } catch (error) {
      console.error('❌ Error sending sensor data:', error.message);
    }
  }

  /**
   * Start continuous data collection
   */
  startDataCollection(intervalMs = 30000) {
    if (this.dataInterval) {
      clearInterval(this.dataInterval);
    }

    console.log(`🔄 Starting data collection every ${intervalMs/1000} seconds`);
    
    // Send initial data
    this.sendSensorData();
    
    // Set up interval for continuous data sending
    this.dataInterval = setInterval(() => {
      this.sendSensorData();
    }, intervalMs);
  }

  /**
   * Stop data collection
   */
  stopDataCollection() {
    if (this.dataInterval) {
      clearInterval(this.dataInterval);
      this.dataInterval = null;
      console.log('🛑 Data collection stopped');
    }
  }

  /**
   * Simulate device shutdown
   */
  shutdown() {
    console.log(`📱 ESP32 Device ${this.deviceId} shutting down...`);
    this.stopDataCollection();
    this.isRunning = false;
    console.log(`✅ ESP32 Device ${this.deviceId} shutdown complete`);
  }

  /**
   * Get device status
   */
  getStatus() {
    return {
      deviceId: this.deviceId,
      isRunning: this.isRunning,
      lastKnownLocation: this.lastKnownLocation,
      dataCollectionActive: this.dataInterval !== null
    };
  }
}

// Example usage and testing
async function runExample() {
  console.log('🚀 Starting ESP32 Integration Example...\n');

  // Create ESP32 simulator
  const esp32 = new ESP32Simulator('esp32-demo');

  try {
    // Simulate device startup
    await esp32.startup();
    
    // Start data collection (every 10 seconds for demo)
    esp32.startDataCollection(10000);
    
    // Let it run for 1 minute
    console.log('📊 Running data collection for 1 minute...\n');
    
    setTimeout(() => {
      console.log('\n📊 Data collection demo complete');
      esp32.shutdown();
      
      // Show final status
      console.log('📱 Final device status:', esp32.getStatus());
      
      process.exit(0);
    }, 60000);
    
  } catch (error) {
    console.error('❌ Example error:', error);
    esp32.shutdown();
    process.exit(1);
  }
}

// Run example if this file is executed directly
if (require.main === module) {
  runExample();
}

module.exports = ESP32Simulator;


