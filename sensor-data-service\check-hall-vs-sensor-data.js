#!/usr/bin/env node

const { FirebaseService, getDatabase } = require('./firebase');

async function checkHallVsSensorData() {
  try {
    console.log('🔍 Checking Hall data vs Sensor_Data...\n');
    
    const db = getDatabase();
    
    // Check hall data structure
    console.log('📂 Checking hall data structure...');
    const hallRef = db.ref('hall');
    const hallSnapshot = await hallRef.once('value');
    
    if (hallSnapshot.exists()) {
      const hallData = hallSnapshot.val();
      console.log('📁 Hall structure:', Object.keys(hallData));
      
      if (hallData.data) {
        const dates = Object.keys(hallData.data);
        console.log('📅 Hall dates:', dates.slice(-5)); // Show last 5 dates
        
        if (dates.length > 0) {
          const latestDate = dates.sort().pop();
          const timeEntries = Object.keys(hallData.data[latestDate]);
          console.log(`⏰ Hall times for ${latestDate}: ${timeEntries.length} entries`);
          console.log('   Sample times:', timeEntries.slice(0, 3));
          
          // Show sample hall data
          const sampleTime = timeEntries[0];
          const sampleData = hallData.data[latestDate][sampleTime];
          console.log(`📊 Sample hall data (${sampleTime}):`, {
            temperature: sampleData.temperature,
            humidity: sampleData.humidity,
            co2: sampleData.co2,
            timestamp: sampleData.timestamp,
            location: sampleData.location
          });
        }
      }
    } else {
      console.log('❌ No hall data found');
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Check Sensor_Data structure
    console.log('📂 Checking Sensor_Data structure...');
    const sensorDataRef = db.ref('Sensor_Data');
    const sensorDataSnapshot = await sensorDataRef.once('value');
    
    if (sensorDataSnapshot.exists()) {
      const sensorData = sensorDataSnapshot.val();
      console.log('📁 Sensor_Data categories:', Object.keys(sensorData));
      
      // Check each category
      for (const category of Object.keys(sensorData)) {
        if (category === '_metadata') continue;
        
        console.log(`\n📂 Category: ${category}`);
        const categoryData = sensorData[category];
        
        if (categoryData && typeof categoryData === 'object') {
          const dates = Object.keys(categoryData).filter(key => key.startsWith('2025-07-'));
          console.log(`   📅 Dates: ${dates.length} (${dates.slice(-3).join(', ')})`);
          
          if (dates.length > 0) {
            const latestDate = dates.sort().pop();
            const timeEntries = Object.keys(categoryData[latestDate]);
            console.log(`   ⏰ Times for ${latestDate}: ${timeEntries.length} entries`);
            console.log('      Sample times:', timeEntries.slice(0, 3));
          }
        }
      }
    } else {
      console.log('❌ No Sensor_Data found');
    }
    
    console.log('\n' + '='.repeat(50) + '\n');
    
    // Check active category
    console.log('🔍 Checking active Sensor_Data category...');
    const activeCategory = await FirebaseService.getActiveSensorDataCategory();
    console.log('📁 Active category:', activeCategory);
    
    // Check active location
    console.log('\n🔍 Checking active location...');
    const activeLocation = await FirebaseService.getActiveLocation();
    console.log('📍 Active location:', activeLocation);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkHallVsSensorData();
