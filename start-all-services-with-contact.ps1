# AirSense Complete Service Startup Script
# Starts all services including the new Contact Service

Write-Host "🚀 Starting AirSense Complete System..." -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Cyan

# Function to start a service in a new PowerShell window
function Start-Service {
    param(
        [string]$ServiceName,
        [string]$Directory,
        [string]$Command,
        [string]$Color = "White"
    )
    
    Write-Host "🔄 Starting $ServiceName..." -ForegroundColor $Color
    
    $scriptBlock = @"
Set-Location '$Directory'
Write-Host '🚀 $ServiceName Starting...' -ForegroundColor $Color
$Command
"@
    
    Start-Process powershell -ArgumentList "-NoExit", "-Command", $scriptBlock
    Start-Sleep -Seconds 2
}

# Start all services
try {
    # 1. Location Service (Port 3002)
    Start-Service -ServiceName "Location Service" -Directory "location-service" -Command "npm run dev" -Color "Green"
    
    # 2. Sensor Data Service (Port 3001)  
    Start-Service -ServiceName "Sensor Data Service" -Directory "sensor-data-service" -Command "npm run dev" -Color "Blue"
    
    # 3. Contact Service (Port 3005) - NEW
    Start-Service -ServiceName "Contact Service" -Directory "contact-service" -Command "npm run dev" -Color "Magenta"
    
    # 4. Admin Interface (Port 3004)
    Start-Service -ServiceName "Admin Interface" -Directory "airsense-admin" -Command "npm start" -Color "Yellow"
    
    # 5. Main React App (Port 5173)
    Start-Service -ServiceName "Main React App" -Directory "airsense-react" -Command "npm run dev:main-only" -Color "Cyan"
    
    Write-Host ""
    Write-Host "✅ All services started successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "📡 Service URLs:" -ForegroundColor White
    Write-Host "  🌐 Main App:        http://localhost:5173" -ForegroundColor Cyan
    Write-Host "  👑 Admin Panel:     http://localhost:3004" -ForegroundColor Yellow
    Write-Host "  📊 Sensor Data:     http://localhost:3001" -ForegroundColor Blue
    Write-Host "  📍 Location Sync:   http://localhost:3002" -ForegroundColor Green
    Write-Host "  📧 Contact Service: http://localhost:3005" -ForegroundColor Magenta
    Write-Host ""
    Write-Host "🔥 Firebase Database: https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 Tips:" -ForegroundColor White
    Write-Host "  • Wait 10-15 seconds for all services to fully start" -ForegroundColor Gray
    Write-Host "  • Check each PowerShell window for service status" -ForegroundColor Gray
    Write-Host "  • Contact form data will be stored in Firebase under 'contacts'" -ForegroundColor Gray
    Write-Host "  • All services have health check endpoints at /health" -ForegroundColor Gray
    Write-Host ""
    Write-Host "🎯 Ready for AirSense development and testing!" -ForegroundColor Green
    
} catch {
    Write-Host "❌ Error starting services: $_" -ForegroundColor Red
    exit 1
}

# Keep this window open
Write-Host ""
Write-Host "Press any key to close this window..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
