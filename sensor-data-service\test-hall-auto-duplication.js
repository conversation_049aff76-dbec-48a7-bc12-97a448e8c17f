#!/usr/bin/env node

/**
 * Test Hall Data Auto-Duplication System
 * 
 * This script tests the automatic duplication of hall data to Sensor_Data
 * by simulating new sensor data being added to the hall section.
 * 
 * Usage: node test-hall-auto-duplication.js [location]
 * Examples:
 *   node test-hall-auto-duplication.js lecture-hall
 *   node test-hall-auto-duplication.js basement
 *   node test-hall-auto-duplication.js deans-office
 */

const { FirebaseService, getDatabase } = require('./firebase');

async function testHallAutoDuplication(testLocation = 'lecture-hall') {
  console.log('🧪 Testing Hall Data Auto-Duplication System...\n');
  
  try {
    // Step 1: Set admin session and active location
    console.log('👑 Step 1: Setting up admin session and location...');
    await FirebaseService.setAdminSession(true, '<EMAIL>');
    await FirebaseService.setActiveLocation(testLocation, '<EMAIL>');
    
    const activeLocation = await FirebaseService.getActiveLocation();
    console.log(`✅ Active location set to: ${activeLocation.location}`);
    
    // Step 2: Generate test sensor data
    console.log('\n📊 Step 2: Generating test sensor data...');
    const now = new Date();
    const date = now.toISOString().split('T')[0]; // 2025-07-21
    const time = now.toTimeString().split(' ')[0]; // 12:34:56
    const timestamp = `${date} ${time}`;
    
    const testSensorData = {
      temperature: 25.5,
      humidity: 65,
      co2: 450,
      pm25: 12,
      pm10: 18,
      gasResistance: 250.5,
      pressure: 1013.25,
      timestamp: timestamp,
      location: 'hall',
      deviceId: 'esp32-test',
      aqi: 45
    };
    
    console.log(`🕐 Test timestamp: ${timestamp}`);
    console.log(`🌡️ Test data:`, {
      temperature: testSensorData.temperature,
      humidity: testSensorData.humidity,
      co2: testSensorData.co2,
      pm25: testSensorData.pm25
    });
    
    // Step 3: Add test data to hall section
    console.log('\n📝 Step 3: Adding test data to hall section...');
    const db = getDatabase();
    const hallRef = db.ref(`hall/data/${date}/${time}`);
    await hallRef.set(testSensorData);
    console.log(`✅ Test data added to hall/data/${date}/${time}`);
    
    // Step 4: Wait for auto-duplication
    console.log('\n⏳ Step 4: Waiting for auto-duplication (5 seconds)...');
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    // Step 5: Verify duplication in Sensor_Data
    console.log('\n🔍 Step 5: Verifying duplication in Sensor_Data...');
    
    const locationToCategoryMap = {
      'lecture-hall': 'Lecture Hall',
      'basement': 'Basement',
      'deans-office': "Dean's Office"
    };
    
    const expectedCategory = locationToCategoryMap[testLocation];
    const sensorDataRef = db.ref(`Sensor_Data/${expectedCategory}/${date}/${time}`);
    const snapshot = await sensorDataRef.once('value');
    
    if (snapshot.exists()) {
      const duplicatedData = snapshot.val();
      console.log(`✅ SUCCESS: Data found in Sensor_Data/${expectedCategory}/${date}/${time}`);
      console.log(`📊 Duplicated data:`, {
        temperature: duplicatedData.temperature,
        humidity: duplicatedData.humidity,
        co2: duplicatedData.co2,
        timestamp: duplicatedData.timestamp,
        category: duplicatedData.category
      });
      
      // Verify timestamp matches
      if (duplicatedData.timestamp === timestamp) {
        console.log(`✅ Timestamp verification: PASSED`);
      } else {
        console.log(`❌ Timestamp verification: FAILED`);
        console.log(`   Expected: ${timestamp}`);
        console.log(`   Got: ${duplicatedData.timestamp}`);
      }
      
    } else {
      console.log(`❌ FAILED: No data found in Sensor_Data/${expectedCategory}/${date}/${time}`);
      
      // Check if category exists
      const categoryRef = db.ref(`Sensor_Data/${expectedCategory}`);
      const categorySnapshot = await categoryRef.once('value');
      
      if (categorySnapshot.exists()) {
        console.log(`📁 Category exists but no data for this timestamp`);
        const categoryData = categorySnapshot.val();
        const dates = Object.keys(categoryData).filter(key => key !== '_metadata');
        console.log(`📅 Available dates in category: ${dates.join(', ')}`);
      } else {
        console.log(`❌ Category ${expectedCategory} does not exist`);
      }
    }
    
    // Step 6: Cleanup test data
    console.log('\n🧹 Step 6: Cleaning up test data...');
    await hallRef.remove();
    if (snapshot.exists()) {
      await sensorDataRef.remove();
    }
    console.log('✅ Test data cleaned up');
    
    // Step 7: Check system status
    console.log('\n📊 Step 7: System status check...');
    try {
      const response = await fetch('http://localhost:3003/health');
      if (response.ok) {
        const healthData = await response.json();
        console.log('🔍 Hall Data Listener Status:', healthData.hallDataListener);
      } else {
        console.log('⚠️ Could not fetch system health status');
      }
    } catch (error) {
      console.log('⚠️ Service may not be running:', error.message);
    }
    
    console.log('\n✅ Hall Data Auto-Duplication Test Complete');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
const testLocation = args[0] || 'lecture-hall';

console.log(`🎯 Testing with location: ${testLocation}`);
console.log('📋 Valid locations: lecture-hall, basement, deans-office\n');

// Run the test
testHallAutoDuplication(testLocation)
  .then(() => {
    console.log('\n🏁 Test execution completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test execution failed:', error);
    process.exit(1);
  });
