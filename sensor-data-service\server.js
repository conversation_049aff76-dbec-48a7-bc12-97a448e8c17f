const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const WebSocket = require('ws');
const http = require('http');
const moment = require('moment');

const { initializeFirebase, FirebaseService } = require('./firebase');
const { dailyAverageService } = require('./daily-averages');
const LocationDataController = require('./location-data-controller');
const AdminSessionService = require('./admin-session-service');
const { hallDataListener } = require('./hall-data-listener');

const app = express();
let PORT = process.env.PORT || 3003;

// Initialize services
let locationDataController;
let adminSessionService;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later.'
});
app.use('/api/', limiter);

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:3001',
    'http://localhost:3002',
    'http://localhost:3004'
  ],
  credentials: true
}));

app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true }));

// Serve static files from public directory (admin interface)
app.use(express.static('public'));

// Create HTTP server
const server = http.createServer(app);

// Create WebSocket server for real-time updates
const wss = new WebSocket.Server({ server });
const clients = new Set();

// WebSocket connection handling
wss.on('connection', (ws, req) => {
  console.log('🔌 New WebSocket connection from:', req.socket.remoteAddress);
  clients.add(ws);

  // Handle client disconnect
  ws.on('close', () => {
    console.log('🔌 WebSocket connection closed');
    clients.delete(ws);
  });

  // Handle errors
  ws.on('error', (error) => {
    console.error('❌ WebSocket error:', error);
    clients.delete(ws);
  });
});

// Broadcast sensor data to all connected clients
function broadcastSensorData(location, data) {
  const message = JSON.stringify({
    type: 'sensor-data-update',
    location: location,
    data: data
  });

  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      try {
        client.send(message);
      } catch (error) {
        console.error('❌ Error sending to client:', error);
        clients.delete(client);
      }
    }
  });

  console.log(`📡 Broadcasted sensor data for ${location} to ${clients.size} clients`);
}

// Validate sensor data
function validateSensorData(data) {
  const requiredFields = ['temperature', 'humidity', 'co2', 'pm25', 'pm10', 'gasResistance'];  // Fixed: use gasResistance instead of voc
  const errors = [];

  for (const field of requiredFields) {
    if (data[field] === undefined || data[field] === null) {
      errors.push(`Missing required field: ${field}`);
    } else if (typeof data[field] !== 'number') {
      errors.push(`Field ${field} must be a number`);
    }
  }

  // Validate ranges
  if (data.temperature < -50 || data.temperature > 100) {
    errors.push('Temperature must be between -50°C and 100°C');
  }
  if (data.humidity < 0 || data.humidity > 100) {
    errors.push('Humidity must be between 0% and 100%');
  }
  if (data.co2 < 0 || data.co2 > 10000) {
    errors.push('CO2 must be between 0 and 10000 ppm');
  }
  if (data.pm25 < 0 || data.pm25 > 1000) {
    errors.push('PM2.5 must be between 0 and 1000 μg/m³');
  }
  if (data.pm10 < 0 || data.pm10 > 1000) {
    errors.push('PM10 must be between 0 and 1000 μg/m³');
  }
  if (data.gasResistance < 0 || data.gasResistance > 10000) {
    errors.push('Gas Resistance must be between 0 and 10000 Ω');
  }

  return {
    isValid: errors.length === 0,
    errors: errors
  };
}

// Calculate AQI from sensor data
function calculateAQI(data) {
  // PM2.5 AQI calculation (primary metric)
  const pm25Breakpoints = [
    { low: 0, high: 12, aqiLow: 0, aqiHigh: 50 },
    { low: 12.1, high: 35.4, aqiLow: 51, aqiHigh: 100 },
    { low: 35.5, high: 55.4, aqiLow: 101, aqiHigh: 150 },
    { low: 55.5, high: 150.4, aqiLow: 151, aqiHigh: 200 },
    { low: 150.5, high: 250.4, aqiLow: 201, aqiHigh: 300 },
    { low: 250.5, high: 500.4, aqiLow: 301, aqiHigh: 500 }
  ];

  const pm25Value = data.pm25 || 0;
  let aqi = 0;

  for (const bp of pm25Breakpoints) {
    if (pm25Value >= bp.low && pm25Value <= bp.high) {
      aqi = Math.round(((bp.aqiHigh - bp.aqiLow) / (bp.high - bp.low)) * (pm25Value - bp.low) + bp.aqiLow);
      break;
    }
  }

  return Math.min(500, Math.max(0, aqi));
}

// API Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'airsense-sensor-data-service',
    timestamp: new Date().toISOString(),
    connectedClients: clients.size,
    firebase: 'connected',
    hallDataListener: hallDataListener.getStatus()
  });
});

// ESP32 sensor data ingestion endpoint (now location-aware)
app.post('/api/sensor-data', async (req, res) => {
  try {
    console.log('📥 Received sensor data:', JSON.stringify(req.body));

    let sensorData = req.body;
    let deviceLocation = req.body.deviceLocation || null;

    // Handle ESP32 format conversion
    if (req.body.gasResistance !== undefined && req.body.pressure !== undefined) {
      // ESP32 format detected - convert to standard format
      console.log('🔄 Converting ESP32 format to standard format');
      sensorData = {
        temperature: req.body.temperature,
        humidity: req.body.humidity,
        co2: req.body.co2,
        pm25: req.body.pm25,
        pm10: req.body.pm10,
        gasResistance: req.body.gasResistance, // Store as gasResistance directly
        pressure: req.body.pressure,
        deviceLocation: deviceLocation
      };
    }

    // Validate sensor data
    const validation = validateSensorData(sensorData);
    if (!validation.isValid) {
      console.error('❌ Validation errors:', validation.errors);
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validation.errors
      });
    }

    // Process sensor data through location-based controller
    const result = await locationDataController.processSensorData(sensorData, deviceLocation);

    if (result.success) {
      // Broadcast to WebSocket clients
      broadcastSensorData(result.location, {
        ...sensorData,
        aqi: result.aqi,
        timestamp: result.timestamp,
        location: result.location
      });

      console.log(`📊 Sensor data processed successfully for location: ${result.location}`);

      res.json({
        success: true,
        message: 'Sensor data stored successfully',
        timestamp: result.timestamp,
        location: result.location,
        aqi: result.aqi,
        deviceId: result.deviceId
      });
    } else {
      console.log(`⚠️ Sensor data not stored: ${result.reason}`);

      res.status(200).json({
        success: false,
        message: result.reason,
        timestamp: result.timestamp,
        dataCollectionActive: false
      });
    }

  } catch (error) {
    console.error('❌ Error processing sensor data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process sensor data',
      details: error.message
    });
  }
});

// Legacy endpoint for backward compatibility
app.post('/api/sensor-data/:location', async (req, res) => {
  try {
    const { location } = req.params;
    const sensorData = req.body;

    // Add device location to the request body
    sensorData.deviceLocation = location;

    // Forward to the new endpoint logic
    const validation = validateSensorData(sensorData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validation.errors
      });
    }

    const result = await locationDataController.processSensorData(sensorData, location);

    if (result.success) {
      broadcastSensorData(result.location, {
        ...sensorData,
        aqi: result.aqi,
        timestamp: result.timestamp,
        location: result.location
      });

      res.json({
        success: true,
        message: 'Sensor data stored successfully',
        timestamp: result.timestamp,
        location: result.location,
        aqi: result.aqi
      });
    } else {
      res.status(200).json({
        success: false,
        message: result.reason,
        timestamp: result.timestamp
      });
    }

  } catch (error) {
    console.error('❌ Error storing sensor data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to store sensor data',
      details: error.message
    });
  }
});

// Get latest sensor data for a location
app.get('/api/sensor-data/:location/latest', async (req, res) => {
  try {
    const { location } = req.params;

    // Map old location names to new category names
    const locationToCategoryMap = {
      'basement': 'Basement',
      'lecture-hall': 'Lecture Hall',
      'deans-office': "Dean's Office"
    };

    const category = locationToCategoryMap[location];

    if (!category) {
      return res.status(400).json({
        success: false,
        error: `Invalid location: ${location}. Valid locations: ${Object.keys(locationToCategoryMap).join(', ')}`
      });
    }

    // Try to get data from new hierarchical structure first
    let latestData = await FirebaseService.getLatestReadingFromHierarchy(category);

    // If no data in new structure, try old structure for backward compatibility
    if (!latestData) {
      latestData = await FirebaseService.getLatestReading(location);
    }

    if (!latestData) {
      return res.status(404).json({
        success: false,
        error: 'No sensor data found for this location'
      });
    }

    res.json({
      success: true,
      location: location,
      category: category,
      data: latestData
    });

  } catch (error) {
    console.error('❌ Error getting latest sensor data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get sensor data',
      details: error.message
    });
  }
});

// Get historical sensor data for a location
app.get('/api/sensor-data/:location/history', async (req, res) => {
  try {
    const { location } = req.params;
    const limit = parseInt(req.query.limit) || 100;

    // Map old location names to new category names
    const locationToCategoryMap = {
      'basement': 'Basement',
      'lecture-hall': 'Lecture Hall',
      'deans-office': "Dean's Office"
    };

    const category = locationToCategoryMap[location];

    if (!category) {
      return res.status(400).json({
        success: false,
        error: `Invalid location: ${location}. Valid locations: ${Object.keys(locationToCategoryMap).join(', ')}`
      });
    }

    // Try to get data from new hierarchical structure first
    let historicalData = await FirebaseService.readSensorDataFromHierarchy(category, limit);

    // If no data in new structure, try old structure for backward compatibility
    if (!historicalData || historicalData.length === 0) {
      historicalData = await FirebaseService.readSensorData(location, limit);
    }

    res.json({
      success: true,
      location: location,
      category: category,
      count: historicalData.length,
      data: historicalData
    });

  } catch (error) {
    console.error('❌ Error getting historical sensor data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get historical data',
      details: error.message
    });
  }
});

// Get daily averages for a location
app.get('/api/sensor-data/:location/daily-averages', async (req, res) => {
  try {
    const { location } = req.params;
    const days = parseInt(req.query.days) || 30;

    const dailyAverages = await FirebaseService.readDailyAverages(location, days);

    res.json({
      success: true,
      location: location,
      days: days,
      count: dailyAverages.length,
      data: dailyAverages
    });

  } catch (error) {
    console.error('❌ Error getting daily averages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get daily averages',
      details: error.message
    });
  }
});

// Manual daily average calculation endpoint
app.post('/api/daily-averages/calculate', async (req, res) => {
  try {
    const { date, location } = req.body;

    if (location) {
      // Calculate for specific location
      const result = await dailyAverageService.calculateLocationDailyAverage(location, date);
      res.json({
        success: true,
        message: 'Daily average calculated successfully',
        location: location,
        date: date,
        data: result
      });
    } else {
      // Calculate for all locations
      await dailyAverageService.calculateDailyAverages(date);
      res.json({
        success: true,
        message: 'Daily averages calculated for all locations',
        date: date
      });
    }

  } catch (error) {
    console.error('❌ Error calculating daily averages:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to calculate daily averages',
      details: error.message
    });
  }
});

// Get daily average calculation status
app.get('/api/daily-averages/status', (req, res) => {
  const status = dailyAverageService.getStatus();
  res.json({
    success: true,
    status: status
  });
});

// Calculate daily averages for date range
app.post('/api/daily-averages/calculate-range', async (req, res) => {
  try {
    const { startDate, endDate, location } = req.body;

    if (!startDate || !endDate) {
      return res.status(400).json({
        success: false,
        error: 'startDate and endDate are required'
      });
    }

    await dailyAverageService.calculateDateRange(startDate, endDate, location);

    res.json({
      success: true,
      message: 'Daily averages calculated for date range',
      startDate: startDate,
      endDate: endDate,
      location: location || 'all locations'
    });

  } catch (error) {
    console.error('❌ Error calculating date range:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to calculate date range',
      details: error.message
    });
  }
});

// ========================================
// Sensor_Data Section Endpoints
// ========================================

// Get all categories in Sensor_Data section
app.get('/api/sensor-data-section/categories', async (req, res) => {
  try {
    console.log('📋 Fetching Sensor_Data categories');

    const categories = await FirebaseService.getSensorDataCategories();

    res.json({
      success: true,
      categories: categories,
      count: categories.length
    });

  } catch (error) {
    console.error('❌ Error fetching categories:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch categories'
    });
  }
});

// Get sensor data for a specific category in Sensor_Data section
app.get('/api/sensor-data-section/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const limit = parseInt(req.query.limit) || 100;

    console.log(`📊 Fetching sensor data for Sensor_Data category: ${category} (limit: ${limit})`);

    const data = await FirebaseService.readSensorDataFromCategory(category, limit);

    res.json({
      success: true,
      category: category,
      count: data.length,
      data: data
    });

  } catch (error) {
    console.error('❌ Error fetching sensor data from category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch sensor data from category'
    });
  }
});

// Post sensor data to a specific category in Sensor_Data section
app.post('/api/sensor-data-section/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const sensorData = req.body;

    console.log(`📝 Receiving sensor data for Sensor_Data category: ${category}`);

    // Validate sensor data
    const validation = validateSensorData(sensorData);
    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid sensor data',
        details: validation.errors
      });
    }

    // Calculate AQI
    const aqi = calculateAQI(sensorData.pm25, sensorData.pm10);
    const enhancedData = {
      ...sensorData,
      aqi: aqi
    };

    // Generate timestamp
    const timestamp = new Date().toISOString();

    // Write to Sensor_Data category
    await FirebaseService.writeSensorDataToCategory(category, timestamp, enhancedData);

    res.json({
      success: true,
      category: category,
      timestamp: timestamp,
      aqi: aqi,
      message: `Sensor data stored in Sensor_Data/${category}`
    });

  } catch (error) {
    console.error('❌ Error storing sensor data to category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to store sensor data to category'
    });
  }
});

// Initialize Sensor_Data section (admin endpoint)
app.post('/api/sensor-data-section/initialize', async (req, res) => {
  try {
    console.log('🚀 Initializing Sensor_Data section...');

    await FirebaseService.initializeSensorDataSection();
    const categories = await FirebaseService.getSensorDataCategories();

    res.json({
      success: true,
      message: 'Sensor_Data section initialized successfully',
      categories: categories,
      count: categories.length
    });

  } catch (error) {
    console.error('❌ Error initializing Sensor_Data section:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to initialize Sensor_Data section'
    });
  }
});

// Set active Sensor_Data category (admin endpoint)
app.post('/api/admin/sensor-data-category', async (req, res) => {
  try {
    const { category, adminEmail } = req.body;

    if (!category) {
      return res.status(400).json({
        success: false,
        error: 'Category is required'
      });
    }

    // Validate category exists
    const categories = await FirebaseService.getSensorDataCategories();
    if (!categories.includes(category)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid category selected',
        availableCategories: categories
      });
    }

    const result = await FirebaseService.setActiveSensorDataCategory(category, adminEmail);

    res.json({
      success: true,
      message: 'Sensor_Data category selected successfully',
      category: category,
      adminEmail: adminEmail || '<EMAIL>',
      timestamp: result.setAt
    });

  } catch (error) {
    console.error('❌ Error setting Sensor_Data category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set Sensor_Data category',
      details: error.message
    });
  }
});

// Get active Sensor_Data category
app.get('/api/admin/sensor-data-category', async (req, res) => {
  try {
    const activeCategory = await FirebaseService.getActiveSensorDataCategory();

    res.json({
      success: true,
      activeCategory: activeCategory
    });

  } catch (error) {
    console.error('❌ Error getting active Sensor_Data category:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get active Sensor_Data category'
    });
  }
});

// Admin Session Management Endpoints

// Admin login endpoint
app.post('/api/admin/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Email and password are required'
      });
    }

    const result = await locationDataController.handleAdminLogin(email, password);

    res.json(result);
  } catch (error) {
    console.error('❌ Admin login error:', error);
    res.status(401).json({
      success: false,
      error: 'Invalid credentials',
      details: error.message
    });
  }
});

// Admin logout endpoint
app.post('/api/admin/logout', async (req, res) => {
  try {
    const { email } = req.body;

    const result = await locationDataController.handleAdminLogout(email);

    res.json(result);
  } catch (error) {
    console.error('❌ Admin logout error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to logout',
      details: error.message
    });
  }
});

// Location selection endpoint
app.post('/api/admin/location', async (req, res) => {
  try {
    const { location, adminEmail } = req.body;

    if (!location) {
      return res.status(400).json({
        success: false,
        error: 'Location is required'
      });
    }

    const result = await locationDataController.handleLocationSelection(location, adminEmail);

    res.json(result);
  } catch (error) {
    console.error('❌ Location selection error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to set location',
      details: error.message
    });
  }
});

// Manual hall data processing endpoint
app.post('/api/admin/process-hall-data', async (req, res) => {
  try {
    const { date, adminEmail } = req.body;

    if (!date) {
      return res.status(400).json({
        success: false,
        error: 'Date is required (format: YYYY-MM-DD)'
      });
    }

    // Verify admin is logged in
    const adminSession = await FirebaseService.getAdminSession();
    if (!adminSession.isLoggedIn) {
      return res.status(401).json({
        success: false,
        error: 'Admin must be logged in'
      });
    }

    // Get current active location and category
    const activeLocation = await FirebaseService.getActiveLocation();
    const activeCategory = await FirebaseService.getActiveSensorDataCategory();

    console.log(`📋 Manual processing request for hall data: ${date}`);
    console.log(`📍 Active location: ${activeLocation}`);
    console.log(`📁 Active category: ${activeCategory}`);

    // Get hall data for the specified date
    const { getDatabase } = require('./firebase');
    const db = getDatabase();
    const hallDataRef = db.ref(`hall/data/${date}`);
    const snapshot = await hallDataRef.once('value');

    if (!snapshot.exists()) {
      return res.status(404).json({
        success: false,
        error: `No hall data found for date: ${date}`
      });
    }

    const hallData = snapshot.val();
    const timeEntries = Object.keys(hallData);

    console.log(`📊 Found ${timeEntries.length} time entries for ${date}`);
    console.log(`🔄 Processing hall data for Sensor_Data duplication...`);

    // Process the hall data
    await processESP32Data(hallData, date);

    res.json({
      success: true,
      message: `Successfully processed hall data for ${date}`,
      date: date,
      timeEntries: timeEntries.length,
      activeLocation: activeLocation,
      activeCategory: activeCategory,
      processedBy: adminEmail || 'admin',
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('❌ Manual hall data processing error:', error);
    res.status(500).json({
      success: false,
      error: error.message
    });
  }
});

// Get admin session status
app.get('/api/admin/status', async (req, res) => {
  try {
    const status = await locationDataController.getStatus();

    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('❌ Error getting admin status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get status',
      details: error.message
    });
  }
});

// Device restart endpoint
app.post('/api/device/restart', async (req, res) => {
  try {
    const result = await locationDataController.handleDeviceRestart();

    res.json({
      success: true,
      message: 'Device restart handled',
      ...result
    });
  } catch (error) {
    console.error('❌ Device restart error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to handle device restart',
      details: error.message
    });
  }
});

// Delayed Storage Management Endpoints

// Get delayed storage queue status
app.get('/api/delayed-storage/status', async (req, res) => {
  try {
    const status = locationDataController.getDelayedStorageStatus();

    res.json({
      success: true,
      status: status
    });
  } catch (error) {
    console.error('❌ Error getting delayed storage status:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get delayed storage status',
      details: error.message
    });
  }
});

// Get pending delayed storage data (for debugging)
app.get('/api/delayed-storage/pending', async (req, res) => {
  try {
    const pendingData = locationDataController.getPendingDelayedData();

    res.json({
      success: true,
      pendingData: pendingData,
      count: pendingData.length
    });
  } catch (error) {
    console.error('❌ Error getting pending delayed data:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to get pending delayed data',
      details: error.message
    });
  }
});

// Clear delayed storage queue (admin only - use with caution)
app.post('/api/delayed-storage/clear', async (req, res) => {
  try {
    const clearedCount = locationDataController.clearDelayedStorageQueue();

    res.json({
      success: true,
      message: `Cleared ${clearedCount} items from delayed storage queue`,
      clearedCount: clearedCount
    });
  } catch (error) {
    console.error('❌ Error clearing delayed storage queue:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to clear delayed storage queue',
      details: error.message
    });
  }
});

// Initialize Firebase and start server
async function startServer() {
  try {
    // Check if port is in use and kill the process
    const tcpPortUsed = require('tcp-port-used');
    const isPortInUse = await tcpPortUsed.check(PORT);
    
    if (isPortInUse) {
      console.log(`⚠️ Port ${PORT} is already in use. Trying to find an available port...`);
      // Try to find an available port
      for (let newPort = PORT + 1; newPort < PORT + 10; newPort++) {
        const isNewPortInUse = await tcpPortUsed.check(newPort);
        if (!isNewPortInUse) {
          console.log(`✅ Found available port: ${newPort}`);
          PORT = newPort;
          break;
        }
      }
    }

    // Initialize Firebase
    await initializeFirebase();

    // Initialize Location Data Controller
    locationDataController = new LocationDataController();
    await locationDataController.initialize();

    // Initialize Admin Session Service
    adminSessionService = new AdminSessionService();

    // Start daily average calculation service
    dailyAverageService.startAutomaticCalculation();

    // Initialize ESP32 Firebase listener
    await initializeESP32Listener();

    // Start Hall Data Auto-Duplication Listener
    await hallDataListener.start();

    // Start server
    server.listen(PORT, () => {
      console.log('🚀 AirSense Sensor Data Service started');
      console.log(`📡 HTTP API: http://localhost:${PORT}`);
      console.log(`🔌 WebSocket: ws://localhost:${PORT}`);
      console.log(`🔥 Firebase: Connected to Realtime Database`);
      console.log(`📊 Ready to receive sensor data from ESP32`);
      console.log(`🔥 ESP32 Firebase listener: Active`);
      console.log(`🔄 Hall data auto-duplication: Active`);
      console.log(`👑 Admin session management: Active`);
      console.log(`📍 Location-based data collection: Active`);
      console.log(`⏰ Delayed storage: 5-minute delay active`);
      console.log(`📈 Daily average calculation: Active`);
      console.log('✅ Service ready for air quality monitoring');
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// ESP32 Data Processing Function - Process individual time entries
const processESP32TimeEntry = async (timeEntry, timeKey, date) => {
  try {
    // Get current admin session to determine location
    const adminStatus = await locationDataController.getStatus();
    const location = adminStatus.session.activeLocation.location || 'lecture-hall'; // Default location

    console.log(`📍 ESP32 data will be stored for location: ${location}`);

    // Convert ESP32 data format to sensor service format
    const sensorData = {
      temperature: timeEntry.temperature || 0,
      humidity: timeEntry.humidity || 0,
      co2: timeEntry.co2 || 0,
      pm25: timeEntry.pm25 || 0,
      pm10: timeEntry.pm10 || 0,
      gasResistance: timeEntry.gasResistance || 0,
      pressure: timeEntry.pressure || 0,
      deviceLocation: location,
      deviceId: 'esp32-main',
      originalTimestamp: timeEntry.timestamp // Use exact timestamp from hall data
    };

    // Process through location data controller
    const result = await locationDataController.processSensorData(sensorData);

    if (result.success) {
      console.log(`✅ ESP32 data processed for ${location} at ${timeKey}`);
      console.log(`📊 AQI: ${result.aqi}, Queue size: ${result.queueSize}`);

      // Broadcast to WebSocket clients
      broadcastSensorData(location, result.sensorData);
    } else {
      console.log(`⚠️ ESP32 data not processed for ${timeKey}: ${result.reason}`);
    }

  } catch (error) {
    console.error(`❌ Error processing ESP32 data for ${timeKey}:`, error);
  }
};

// ESP32 Data Processing Function - Process all time entries for a date
const processESP32Data = async (esp32Data, timestamp) => {
  try {
    console.log(`📡 Processing ESP32 data for date: ${timestamp}`);
    console.log(`📊 Found ${Object.keys(esp32Data).length} time entries`);

    // Process each time entry individually
    for (const [timeKey, timeEntry] of Object.entries(esp32Data)) {
      if (timeEntry && typeof timeEntry === 'object' && timeEntry.timestamp) {
        await processESP32TimeEntry(timeEntry, timeKey, timestamp);
      } else {
        console.log(`⚠️ Skipping invalid time entry: ${timeKey}`);
      }
    }

    console.log(`✅ Completed processing all time entries for ${timestamp}`);

  } catch (error) {
    console.error('❌ Error processing ESP32 data:', error);
  }
};

// Initialize ESP32 Firebase Listener (DISABLED to prevent duplication)
const initializeESP32Listener = async () => {
  console.log('🔥 ESP32 Firebase listener setup...');
  console.log('🚫 AUTOMATIC hall data processing is DISABLED to prevent duplication');
  console.log('✅ Hall data will only be processed when ESP32 sends NEW data');
  console.log('📋 Existing hall data will NOT be reprocessed');

  // NOTE: The automatic Firebase listener is disabled to prevent reprocessing
  // existing hall data every time the service restarts. This was causing
  // the same data to be duplicated multiple times in different Sensor_Data categories.

  // Future enhancement: Implement a proper mechanism to detect truly NEW ESP32 data
  // For now, hall data duplication to Sensor_Data will happen only when:
  // 1. ESP32 sends completely new data to hall/data/
  // 2. Manual processing is triggered via API endpoint

  console.log('✅ ESP32 Firebase listener configured (duplication prevention active)');
};

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down sensor data service...');
  dailyAverageService.stopAutomaticCalculation();

  if (locationDataController) {
    locationDataController.shutdown();
  }

  // Stop hall data listener
  hallDataListener.stop();

  server.close(() => {
    console.log('✅ Sensor data service stopped');
    process.exit(0);
  });
});

// Start the server
startServer();



