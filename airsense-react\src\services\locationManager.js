// Centralized Location Management Service
import { sensorDataService } from './sensorDataService';

class LocationManager {
  constructor() {
    this.currentLocation = null;
    this.listeners = new Set();
    this.isInitialized = false;
    this.initializationPromise = null;
    this.syncInterval = null;
    this.lastSyncTime = 0;
    this.syncIntervalMs = 300000; // 5 minutes to minimize API calls
  }

  // Initialize location manager
  async initialize() {
    if (this.isInitialized) {
      return this.currentLocation;
    }

    if (this.initializationPromise) {
      return this.initializationPromise;
    }

    this.initializationPromise = this._performInitialization();
    return this.initializationPromise;
  }

  async _performInitialization() {
    console.log('🔄 LocationManager: Initializing...');

    try {
      // Try to get location from sensor service first with timeout
      console.log('🔄 LocationManager: Fetching from sensor service...');
      let backendLocation = null;

      try {
        backendLocation = await Promise.race([
          sensorDataService.getCurrentActiveLocation(),
          new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
        ]);
      } catch (error) {
        console.warn('⚠️ LocationManager: Sensor service error:', error.message);
      }

      if (backendLocation) {
        console.log('📍 LocationManager: Got location from backend:', backendLocation);
        this.currentLocation = backendLocation;
        localStorage.setItem('userLocation', backendLocation);
      } else {
        console.log('⚠️ LocationManager: Backend unavailable/rate limited, using fallback');
        // Fallback to localStorage
        const storedLocation = localStorage.getItem('userLocation') || 'deans-office'; // Default to dean's office since that's current
        console.log('📍 LocationManager: Using stored location:', storedLocation);
        this.currentLocation = storedLocation;
      }

      this.isInitialized = true;
      this.lastSyncTime = Date.now();

      // Start periodic sync
      this.startPeriodicSync();

      // Notify all listeners
      this.notifyListeners(this.currentLocation);

      console.log('✅ LocationManager: Initialized with location:', this.currentLocation);
      return this.currentLocation;

    } catch (error) {
      console.error('❌ LocationManager: Initialization failed:', error);

      // Fallback to localStorage on error
      const storedLocation = localStorage.getItem('userLocation') || 'basement';
      console.log('📍 LocationManager: Error fallback to stored location:', storedLocation);
      this.currentLocation = storedLocation;
      this.isInitialized = true;

      // Still start periodic sync to retry later
      this.startPeriodicSync();

      this.notifyListeners(this.currentLocation);
      return this.currentLocation;
    }
  }

  // Get current location
  getCurrentLocation() {
    if (!this.isInitialized) {
      console.warn('⚠️ LocationManager: Not initialized, returning stored location');
      const stored = localStorage.getItem('userLocation') || 'basement';
      // Try to initialize in background
      this.initialize().catch(err => console.error('Background init failed:', err));
      return stored;
    }
    return this.currentLocation || localStorage.getItem('userLocation') || 'basement';
  }

  // Add listener for location changes
  addListener(callback) {
    this.listeners.add(callback);
    
    // If already initialized, immediately notify with current location
    if (this.isInitialized && this.currentLocation) {
      callback(this.currentLocation);
    }
  }

  // Remove listener
  removeListener(callback) {
    this.listeners.delete(callback);
  }

  // Notify all listeners
  notifyListeners(location) {
    this.listeners.forEach(callback => {
      try {
        callback(location);
      } catch (error) {
        console.error('❌ LocationManager: Error in listener callback:', error);
      }
    });
  }

  // Start periodic sync with backend
  startPeriodicSync() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
    }

    this.syncInterval = setInterval(async () => {
      await this.syncWithBackend();
    }, this.syncIntervalMs);
  }

  // Sync with backend
  async syncWithBackend() {
    try {
      const now = Date.now();

      // Skip if we synced recently (prevent excessive calls)
      if (now - this.lastSyncTime < this.syncIntervalMs) {
        return;
      }

      console.log('🔄 LocationManager: Syncing with backend...');

      // Add timeout to prevent hanging
      const backendLocation = await Promise.race([
        sensorDataService.getCurrentActiveLocation(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Sync timeout')), 5000))
      ]);

      if (backendLocation && backendLocation !== this.currentLocation) {
        console.log('📍 LocationManager: Location changed:', this.currentLocation, '->', backendLocation);
        this.currentLocation = backendLocation;
        localStorage.setItem('userLocation', backendLocation);
        this.notifyListeners(backendLocation);
      } else if (backendLocation) {
        console.log('📍 LocationManager: Location unchanged:', backendLocation);
      } else {
        console.log('⚠️ LocationManager: Backend returned null during sync');
      }

      this.lastSyncTime = now;

    } catch (error) {
      console.error('❌ LocationManager: Sync failed:', error);
      // Don't update lastSyncTime on error to allow retry sooner
    }
  }

  // Force refresh from backend
  async forceRefresh() {
    console.log('🔄 LocationManager: Force refresh requested...');

    try {
      // Add timeout to prevent hanging
      const backendLocation = await Promise.race([
        sensorDataService.getCurrentActiveLocation(),
        new Promise((_, reject) => setTimeout(() => reject(new Error('Force refresh timeout')), 5000))
      ]);

      if (backendLocation) {
        if (backendLocation !== this.currentLocation) {
          console.log('📍 LocationManager: Force refresh - location changed:', backendLocation);
          this.currentLocation = backendLocation;
          localStorage.setItem('userLocation', backendLocation);
          this.lastSyncTime = Date.now(); // Update sync time

          // Ensure we're initialized
          if (!this.isInitialized) {
            this.isInitialized = true;
            this.startPeriodicSync();
          }

          this.notifyListeners(backendLocation);
        } else {
          console.log('📍 LocationManager: Force refresh - location unchanged:', backendLocation);
        }
        return backendLocation;
      } else {
        console.warn('⚠️ LocationManager: Force refresh returned null');

        // If not initialized yet, try to initialize with fallback
        if (!this.isInitialized) {
          const storedLocation = localStorage.getItem('userLocation') || 'basement';
          this.currentLocation = storedLocation;
          this.isInitialized = true;
          this.startPeriodicSync();
          console.log('📍 LocationManager: Initialized with stored location during force refresh:', storedLocation);
          return storedLocation;
        }

        return this.currentLocation;
      }
    } catch (error) {
      console.error('❌ LocationManager: Force refresh error:', error);

      // If not initialized yet, try to initialize with fallback
      if (!this.isInitialized) {
        const storedLocation = localStorage.getItem('userLocation') || 'basement';
        this.currentLocation = storedLocation;
        this.isInitialized = true;
        this.startPeriodicSync();
        console.log('📍 LocationManager: Initialized with stored location after error:', storedLocation);
        return storedLocation;
      }

      return this.currentLocation;
    }
  }

  // Cleanup
  cleanup() {
    if (this.syncInterval) {
      clearInterval(this.syncInterval);
      this.syncInterval = null;
    }
    this.listeners.clear();
    this.isInitialized = false;
    this.initializationPromise = null;
  }

  // Get location display name
  getLocationDisplayName(locationKey = null) {
    const location = locationKey || this.currentLocation;

    // Handle empty or null locations
    if (!location || location === '') {
      return 'No Location Set';
    }

    const locationNames = {
      'deans-office': "Dean's Office",
      'lecture-hall': 'Lecture Hall',
      'basement': 'Basement'
    };
    return locationNames[location] || location;
  }
}

// Export singleton instance
export const locationManager = new LocationManager();
export default locationManager;
