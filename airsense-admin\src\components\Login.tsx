import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import '../styles/Login.css';

const Login: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    // Add admin-login-page class to body for background styling
    document.body.classList.add('admin-login-page');
    
    // Cleanup function to remove class when component unmounts
    return () => {
      document.body.classList.remove('admin-login-page');
    };
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      // Call the backend to handle admin login
      const response = await fetch('http://localhost:3003/api/admin/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: email,
          password: password
        })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Store admin authentication
        localStorage.setItem('isAdmin', 'true');

        // Dispatch custom event to notify other tabs/windows about admin login
        window.dispatchEvent(new Event('adminStatusChanged'));

        // Also dispatch storage event for cross-tab communication
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'isAdmin',
          newValue: 'true',
          storageArea: localStorage
        }));

        console.log('👑 Admin logged in successfully:', data.message);
        navigate('/location-selection');
      } else {
        setError(data.error || 'Invalid email or password');
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      setError('Login service unavailable. Please try again.');
    }
  };

  return (
    <div className="login-container">
      <div className="login-card">
        <div className="login-header">
          <img src="/logo.jpg" alt="AirSense Logo" className="login-logo" />
          <h1>Admin Login</h1>
          <p>Access the Air Quality Monitoring System</p>
        </div>
        
        <form onSubmit={handleSubmit} className="login-form">
          {error && <div className="error-message">{error}</div>}
          
          <div className="form-group">
            <label htmlFor="email">Email Address</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              placeholder="Enter your email"
            />
          </div>
          
          <div className="form-group">
            <label htmlFor="password">Password</label>
            <input
              type="password"
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
              placeholder="Enter your password"
            />
          </div>
          
          <button type="submit" className="login-button">
            Login
          </button>
        </form>
        
        <div className="login-footer">
          <p>Default credentials: <EMAIL> / admin123</p>
        </div>
      </div>
    </div>
  );
};

export default Login;
