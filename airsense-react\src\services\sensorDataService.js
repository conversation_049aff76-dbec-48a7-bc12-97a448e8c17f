// Sensor Data Service for Firebase Integration
const SENSOR_DATA_API_BASE = 'http://localhost:3003/api';

class SensorDataService {
  constructor() {
    this.websocket = null;
    this.listeners = new Set();
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectDelay = 1000;

    // Location caching to prevent excessive API calls
    this.cachedLocation = null;
    this.lastLocationFetch = 0;
    this.locationCacheTimeout = 120000; // 2 minutes cache to reduce API calls
    this.isLocationFetching = false;
    this.rateLimitedUntil = 0; // Track when rate limiting should clear
  }

  // Initialize WebSocket connection for real-time updates
  initializeWebSocket() {
    try {
      this.websocket = new WebSocket('ws://localhost:3003');
      
      this.websocket.onopen = () => {
        console.log('🔌 Connected to sensor data WebSocket');
        this.reconnectAttempts = 0;
      };

      this.websocket.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          if (message.type === 'sensor-data-update') {
            this.notifyListeners(message.location, message.data);
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      this.websocket.onclose = () => {
        console.log('🔌 WebSocket connection closed');
        this.attemptReconnect();
      };

      this.websocket.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
      this.attemptReconnect();
    }
  }

  // Attempt to reconnect WebSocket
  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`🔄 Attempting to reconnect WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
      
      setTimeout(() => {
        this.initializeWebSocket();
      }, this.reconnectDelay * this.reconnectAttempts);
    } else {
      console.error('❌ Max WebSocket reconnection attempts reached');
    }
  }

  // Add listener for real-time updates
  addListener(callback) {
    this.listeners.add(callback);
    
    // Initialize WebSocket if not already connected
    if (!this.websocket || this.websocket.readyState === WebSocket.CLOSED) {
      this.initializeWebSocket();
    }
  }

  // Remove listener
  removeListener(callback) {
    this.listeners.delete(callback);
    
    // Close WebSocket if no listeners
    if (this.listeners.size === 0 && this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  // Notify all listeners of new data
  notifyListeners(location, data) {
    this.listeners.forEach(callback => {
      try {
        callback(location, data);
      } catch (error) {
        console.error('❌ Error in listener callback:', error);
      }
    });
  }

  // API Methods

  // Get current active location from sensor data service with caching
  async getCurrentActiveLocation() {
    const now = Date.now();

    // Check if we're still rate limited
    if (this.rateLimitedUntil > now) {
      console.log('⏳ SensorDataService: Still rate limited, using cached location');
      return this.cachedLocation || localStorage.getItem('userLocation') || null;
    }

    // Return cached location if still valid and not expired
    if (this.cachedLocation && (now - this.lastLocationFetch) < this.locationCacheTimeout) {
      console.log('� SensorDataService: Using cached location:', this.cachedLocation);
      return this.cachedLocation;
    }

    // Prevent multiple simultaneous requests
    if (this.isLocationFetching) {
      console.log('🔄 SensorDataService: Location fetch already in progress, waiting...');
      // Wait for ongoing request to complete
      while (this.isLocationFetching) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
      return this.cachedLocation;
    }

    this.isLocationFetching = true;
    console.log('🔄 SensorDataService: Fetching fresh location from backend...');

    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/admin/status`);

      if (!response.ok) {
        if (response.status === 429) {
          console.warn('⚠️ SensorDataService: Rate limited, backing off for 5 minutes');
          // Set rate limit backoff for 5 minutes
          this.rateLimitedUntil = Date.now() + (5 * 60 * 1000);
          // Return cached location or localStorage if available during rate limiting
          const fallbackLocation = this.cachedLocation || localStorage.getItem('userLocation');
          if (fallbackLocation) {
            console.log('📍 SensorDataService: Using fallback location during rate limit:', fallbackLocation);
            return fallbackLocation;
          }
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();

      if (result.success && result.status) {
        // Get location from multiple possible sources in the response
        const activeLocation = result.status.session?.activeLocation?.location ||
                              result.status.controller?.lastKnownLocation ||
                              result.status.session?.dataCollection?.location;

        if (activeLocation) {
          this.cachedLocation = activeLocation;
          this.lastLocationFetch = now;
          console.log('📍 SensorDataService: Fresh location cached:', activeLocation);

          // Update localStorage to keep it in sync
          localStorage.setItem('userLocation', activeLocation);

          return activeLocation;
        } else {
          console.warn('⚠️ SensorDataService: No active location found in response');
          return null;
        }
      } else {
        console.warn('⚠️ SensorDataService: Invalid response structure');
        return null;
      }
    } catch (error) {
      console.error('❌ SensorDataService: Error fetching active location:', error);

      // Return cached location if available, even if expired
      if (this.cachedLocation) {
        console.log('📍 SensorDataService: Returning cached location due to error:', this.cachedLocation);
        return this.cachedLocation;
      }

      return null;
    } finally {
      this.isLocationFetching = false;
    }
  }

  // Get latest sensor data for a location
  async getLatestSensorData(location) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}/latest`);
      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch latest sensor data');
      }
    } catch (error) {
      console.error(`❌ Error fetching latest data for ${location}:`, error);
      throw error;
    }
  }

  // Get historical sensor data for a location
  async getHistoricalSensorData(location, limit = 100) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}/history?limit=${limit}`);
      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch historical sensor data');
      }
    } catch (error) {
      console.error(`❌ Error fetching historical data for ${location}:`, error);
      throw error;
    }
  }

  // Get daily averages for a location
  async getDailyAverages(location, days = 30) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}/daily-averages?days=${days}`);
      const result = await response.json();
      
      if (result.success) {
        return result.data;
      } else {
        throw new Error(result.error || 'Failed to fetch daily averages');
      }
    } catch (error) {
      console.error(`❌ Error fetching daily averages for ${location}:`, error);
      throw error;
    }
  }

  // Get sensor data for all locations
  async getAllLocationData() {
    const locations = ['deans-office', 'lecture-hall', 'basement'];
    const results = {};
    
    try {
      const promises = locations.map(async (location) => {
        try {
          const data = await this.getLatestSensorData(location);
          results[location] = data;
        } catch (error) {
          console.warn(`⚠️ No data available for ${location}`);
          results[location] = null;
        }
      });
      
      await Promise.all(promises);
      return results;
    } catch (error) {
      console.error('❌ Error fetching all location data:', error);
      throw error;
    }
  }

  // Send sensor data (for ESP32 simulation)
  async sendSensorData(location, sensorData) {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${location}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(sensorData),
      });
      
      const result = await response.json();
      
      if (result.success) {
        return result;
      } else {
        throw new Error(result.error || 'Failed to send sensor data');
      }
    } catch (error) {
      console.error(`❌ Error sending sensor data for ${location}:`, error);
      throw error;
    }
  }

  // Check service health
  async checkHealth() {
    try {
      const response = await fetch(`${SENSOR_DATA_API_BASE.replace('/api', '')}/health`);
      const result = await response.json();
      return result;
    } catch (error) {
      console.error('❌ Error checking service health:', error);
      throw error;
    }
  }

  // Format sensor data for display
  formatSensorData(data) {
    if (!data) return null;

    return {
      temperature: parseFloat(data.temperature?.toFixed(1)) || 0,
      humidity: parseFloat(data.humidity?.toFixed(1)) || 0,
      co2: Math.round(data.co2) || 0,
      pm25: parseFloat(data.pm25?.toFixed(1)) || 0,
      pm10: parseFloat(data.pm10?.toFixed(1)) || 0,
      gasResistance: Math.round(data.gasResistance) || 0,  // Fixed: use gasResistance instead of voc
      aqi: Math.round(data.aqi) || 0,
      timestamp: data.timestamp,
      location: data.location
    };
  }

  // Get air quality status based on AQI
  getAirQualityStatus(aqi) {
    if (aqi <= 50) return { status: 'Good', color: '#00e400', level: 'good' };
    if (aqi <= 100) return { status: 'Moderate', color: '#ffff00', level: 'moderate' };
    if (aqi <= 150) return { status: 'Unhealthy for Sensitive Groups', color: '#ff7e00', level: 'moderate' };
    if (aqi <= 200) return { status: 'Unhealthy', color: '#ff0000', level: 'poor' };
    if (aqi <= 300) return { status: 'Very Unhealthy', color: '#8f3f97', level: 'poor' };
    return { status: 'Hazardous', color: '#7e0023', level: 'poor' };
  }

  // Convert location key to display name
  getLocationDisplayName(locationKey) {
    const locationNames = {
      'deans-office': "Dean's Office",
      'lecture-hall': 'Lecture Hall',
      'basement': 'Basement'
    };
    return locationNames[locationKey] || locationKey;
  }

  // Cleanup resources
  cleanup() {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
    this.listeners.clear();
  }
}

// Export singleton instance
export const sensorDataService = new SensorDataService();
export default sensorDataService;
