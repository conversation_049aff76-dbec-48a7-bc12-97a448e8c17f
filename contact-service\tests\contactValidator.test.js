/**
 * Contact Form Validation Tests
 */

const {
  isValidEmail,
  validateName,
  validateEmail,
  validateSubject,
  validateMessage,
  validateContactForm,
  sanitizeContactData,
  isCompleteContactData
} = require('../validators/contactValidator');

describe('Contact Form Validation', () => {
  
  describe('Email Validation', () => {
    test('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];
      
      validEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(true);
      });
    });
    
    test('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        '<EMAIL>',
        'user@.com',
        '',
        null,
        undefined
      ];
      
      invalidEmails.forEach(email => {
        expect(isValidEmail(email)).toBe(false);
      });
    });
  });
  
  describe('Name Validation', () => {
    test('should accept valid names', () => {
      const validNames = [
        '<PERSON> <PERSON>e',
        '<PERSON> <PERSON>',
        "O'<PERSON>",
        'Jean-Pierre',
        'A B'
      ];
      
      validNames.forEach(name => {
        expect(validateName(name)).toBeNull();
      });
    });
    
    test('should reject invalid names', () => {
      expect(validateName('')).toContain('required');
      expect(validateName('A')).toContain('at least 2 characters');
      expect(validateName('A'.repeat(101))).toContain('less than 100 characters');
      expect(validateName('John123')).toContain('only contain letters');
      expect(validateName(null)).toContain('required');
    });
  });
  
  describe('Subject Validation', () => {
    test('should accept valid subjects', () => {
      expect(validateSubject('Hello World')).toBeNull();
      expect(validateSubject('Question about AirSense')).toBeNull();
    });
    
    test('should reject invalid subjects', () => {
      expect(validateSubject('')).toContain('required');
      expect(validateSubject('Hi')).toContain('at least 5 characters');
      expect(validateSubject('A'.repeat(201))).toContain('less than 200 characters');
      expect(validateSubject(null)).toContain('required');
    });
  });
  
  describe('Message Validation', () => {
    test('should accept valid messages', () => {
      expect(validateMessage('This is a test message for validation')).toBeNull();
    });
    
    test('should reject invalid messages', () => {
      expect(validateMessage('')).toContain('required');
      expect(validateMessage('Short')).toContain('at least 10 characters');
      expect(validateMessage('A'.repeat(2001))).toContain('less than 2000 characters');
      expect(validateMessage(null)).toContain('required');
    });
  });
  
  describe('Complete Form Validation', () => {
    const validFormData = {
      name: 'John Doe',
      email: '<EMAIL>',
      subject: 'Test Subject',
      message: 'This is a test message for the contact form validation.'
    };
    
    test('should pass validation for complete valid form', () => {
      const errors = validateContactForm(validFormData);
      expect(errors).toHaveLength(0);
    });
    
    test('should return errors for invalid form data', () => {
      const invalidFormData = {
        name: 'A',
        email: 'invalid-email',
        subject: 'Hi',
        message: 'Short'
      };
      
      const errors = validateContactForm(invalidFormData);
      expect(errors.length).toBeGreaterThan(0);
      expect(errors.some(error => error.includes('Name'))).toBe(true);
      expect(errors.some(error => error.includes('Email'))).toBe(true);
      expect(errors.some(error => error.includes('Subject'))).toBe(true);
      expect(errors.some(error => error.includes('Message'))).toBe(true);
    });
    
    test('should return errors for missing fields', () => {
      const incompleteFormData = {
        name: 'John Doe'
        // Missing email, subject, message
      };
      
      const errors = validateContactForm(incompleteFormData);
      expect(errors.length).toBeGreaterThan(0);
    });
  });
  
  describe('Data Sanitization', () => {
    test('should sanitize form data correctly', () => {
      const rawData = {
        name: '  John Doe  ',
        email: '  <EMAIL>  ',
        subject: '  Test Subject  ',
        message: '  This is a test message.  '
      };
      
      const sanitized = sanitizeContactData(rawData);
      
      expect(sanitized.name).toBe('John Doe');
      expect(sanitized.email).toBe('<EMAIL>');
      expect(sanitized.subject).toBe('Test Subject');
      expect(sanitized.message).toBe('This is a test message.');
    });
    
    test('should handle null/undefined values', () => {
      const rawData = {
        name: null,
        email: undefined,
        subject: '',
        message: '  '
      };
      
      const sanitized = sanitizeContactData(rawData);
      
      expect(sanitized.name).toBe('');
      expect(sanitized.email).toBe('');
      expect(sanitized.subject).toBe('');
      expect(sanitized.message).toBe('');
    });
  });
  
  describe('Completeness Check', () => {
    test('should identify complete data', () => {
      const completeData = {
        name: 'John Doe',
        email: '<EMAIL>',
        subject: 'Test',
        message: 'Test message'
      };
      
      expect(isCompleteContactData(completeData)).toBe(true);
    });
    
    test('should identify incomplete data', () => {
      const incompleteData = {
        name: 'John Doe',
        email: '<EMAIL>'
        // Missing subject and message
      };
      
      expect(isCompleteContactData(incompleteData)).toBe(false);
    });
  });
});
