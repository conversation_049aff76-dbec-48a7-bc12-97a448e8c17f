@echo off
echo Starting AirSense Applications...
echo.

echo Starting Main App (airsense-react) on port 5174...
start "AirSense Main App" cmd /k "cd airsense-react && npm run dev"

echo.
echo Waiting 3 seconds before starting admin app...
timeout /t 3 /nobreak > nul

echo Starting Admin App (airsense-admin) on port 3001...
start "AirSense Admin App" cmd /k "cd airsense-admin && npm start"

echo.
echo Both applications are starting...
echo.
echo Main App: http://localhost:5174/
echo Admin App: http://localhost:3001/
echo.
echo Press any key to exit this window...
pause > nul
