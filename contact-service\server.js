const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const admin = require('firebase-admin');
const path = require('path');
const { validateContactForm, sanitizeContactData } = require('./validators/contactValidator');

// Initialize Express app
const app = express();
const PORT = process.env.PORT || 3005;

// Security middleware
app.use(helmet());

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 requests per windowMs
  message: {
    success: false,
    error: 'Too many contact form submissions, please try again later.'
  }
});

// CORS configuration
app.use(cors({
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:3001',
    'http://localhost:3003',
    'http://localhost:3004'
  ],
  credentials: true
}));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Firebase configuration
const FIREBASE_CONFIG = {
  databaseURL: 'https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/',
  projectId: 'test-2-c0fd4'
};

// Initialize Firebase Admin SDK
let firebaseApp = null;
let database = null;

function initializeFirebase() {
  try {
    if (!firebaseApp) {
      // Load service account key from sensor-data-service directory
      const serviceAccount = require('../sensor-data-service/serviceAccountKey.json');
      
      // Initialize Firebase Admin
      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: FIREBASE_CONFIG.databaseURL
      }, 'contact-service');
      
      // Get database reference
      database = admin.database(firebaseApp);
      
      console.log('🔥 Firebase Admin SDK initialized successfully for Contact Service');
      console.log(`📊 Database URL: ${FIREBASE_CONFIG.databaseURL}`);
      console.log(`🏗️ Project ID: ${FIREBASE_CONFIG.projectId}`);
    }
    
    return { app: firebaseApp, database };
  } catch (error) {
    console.error('❌ Firebase initialization error:', error);
    throw error;
  }
}

// Get database reference
function getDatabase() {
  if (!database) {
    initializeFirebase();
  }
  return database;
}

// Database paths
const DB_PATHS = {
  contacts: 'contacts',
  contactWithId: (id) => `contacts/${id}`
};

// Sanitize input data for Firebase storage
function prepareContactDataForStorage(data) {
  const sanitized = sanitizeContactData(data);
  return {
    ...sanitized,
    timestamp: admin.database.ServerValue.TIMESTAMP,
    submittedAt: new Date().toISOString(),
    status: 'new',
    ipAddress: null // Will be set in the route handler
  };
}

// API Routes

// Health check
app.get('/health', (req, res) => {
  res.json({
    status: 'healthy',
    service: 'airsense-contact-service',
    timestamp: new Date().toISOString(),
    firebase: database ? 'connected' : 'disconnected'
  });
});

// Contact form submission endpoint
app.post('/api/contact', limiter, async (req, res) => {
  try {
    const contactData = req.body;
    
    // Validate input data
    const validationErrors = validateContactForm(contactData);
    if (validationErrors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: validationErrors
      });
    }
    
    // Sanitize and prepare data for storage
    const sanitizedData = prepareContactDataForStorage(contactData);
    sanitizedData.ipAddress = req.ip || req.connection.remoteAddress;
    
    // Generate unique ID for the contact submission
    const contactId = `contact_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Store in Firebase
    const db = getDatabase();
    const contactRef = db.ref(DB_PATHS.contactWithId(contactId));
    
    await contactRef.set(sanitizedData);
    
    console.log(`📧 New contact form submission stored: ${contactId}`);
    console.log(`👤 From: ${sanitizedData.name} (${sanitizedData.email})`);
    console.log(`📝 Subject: ${sanitizedData.subject}`);
    
    res.json({
      success: true,
      message: 'Contact form submitted successfully',
      contactId: contactId,
      timestamp: new Date().toISOString()
    });
    
  } catch (error) {
    console.error('❌ Error processing contact form:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to process contact form submission',
      details: error.message
    });
  }
});

// Get all contact submissions (admin only - for future use)
app.get('/api/contacts', async (req, res) => {
  try {
    const db = getDatabase();
    const contactsRef = db.ref(DB_PATHS.contacts);
    const snapshot = await contactsRef.once('value');
    const contacts = snapshot.val() || {};
    
    // Convert to array and sort by timestamp (newest first)
    const contactsArray = Object.keys(contacts).map(id => ({
      id,
      ...contacts[id]
    })).sort((a, b) => new Date(b.submittedAt) - new Date(a.submittedAt));
    
    res.json({
      success: true,
      contacts: contactsArray,
      count: contactsArray.length
    });
    
  } catch (error) {
    console.error('❌ Error fetching contacts:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch contact submissions',
      details: error.message
    });
  }
});

// Start server
async function startServer() {
  try {
    // Initialize Firebase
    initializeFirebase();
    
    // Start server
    app.listen(PORT, () => {
      console.log('🚀 AirSense Contact Service started');
      console.log(`📡 HTTP API: http://localhost:${PORT}`);
      console.log(`🔥 Firebase: Connected to Realtime Database`);
      console.log(`📧 Ready to receive contact form submissions`);
      console.log(`🛡️ Security: Rate limiting and validation active`);
      console.log('✅ Service ready for contact form processing');
    });
  } catch (error) {
    console.error('❌ Failed to start server:', error);
    process.exit(1);
  }
}

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('🛑 Shutting down contact service...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('🛑 Shutting down contact service...');
  process.exit(0);
});

// Start the server
startServer().catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
