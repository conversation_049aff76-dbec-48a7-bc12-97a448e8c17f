import React, { useState, useEffect, useRef } from 'react';
import Layout from '../layouts/Layout';
import { Chart, ChartConfiguration, registerables } from 'chart.js';
import '../styles/Comparison.css';

Chart.register(...registerables);

// Backend API configuration
const SENSOR_DATA_API_BASE = 'http://localhost:3003/api';

// Location mapping for API calls
const locationMapping: { [key: string]: string } = {
  'lectureHall': 'lecture-hall',
  'deansOffice': 'deans-office',
  'basement': 'basement'
};

// Metric field mapping for Firebase data
const metricFieldMap: { [key: string]: string } = {
  'Temperature': 'temperature',
  'Humidity': 'humidity',
  'PM2.5': 'pm25',
  'PM10': 'pm10',
  'CO2 Level': 'co2',
  'Gas Resistance': 'gasResistance',
  'Pressure': 'pressure',
  'Nitrogen Dioxide (NO2)': 'no2',
  'Ozone (O3)': 'o3'
};

// Base Air Quality Standards Template
const baseAirQualityStandards = {
  Temperature: { 
    poor: { min: -Infinity, max: 18, unit: '°C' },
    moderate: { min: 18, max: 21.8, unit: '°C' },
    good: { min: 21.8, max: 26.1, unit: '°C' },
    moderate2: { min: 26.1, max: 30, unit: '°C' },
    poor2: { min: 30, max: Infinity, unit: '°C' }
  },
  Humidity: {
    poor: { min: -Infinity, max: 20, unit: '%' },
    moderate: { min: 20, max: 30, unit: '%' },
    good: { min: 30, max: 60, unit: '%' },
    moderate2: { min: 60, max: 70, unit: '%' },
    poor2: { min: 70, max: Infinity, unit: '%' }
  },
  'CO2 Level': {
    good: { min: 400, max: 800, unit: 'ppm' },
    moderate: { min: 800, max: 1200, unit: 'ppm' },
    poor: { min: 1200, max: Infinity, unit: 'ppm' }
  },
  'PM2.5': {
    good: { min: 0, max: 100, unit: 'µg/m³' },
    moderate: { min: 100, max: 125, unit: 'µg/m³' },
    poor: { min: 125, max: Infinity, unit: 'µg/m³' }
  },
  'PM10': {
    good: { min: 0, max: 200, unit: 'µg/m³' },
    moderate: { min: 200, max: 250, unit: 'µg/m³' },
    poor: { min: 250, max: Infinity, unit: 'µg/m³' }
  },
 'Gas Resistance': {
    good: { min: 50, max: Infinity, unit: 'kΩ' },
    moderate: { min: 10, max: 50, unit: 'kΩ' },
    poor: { min: 0, max: 10, unit: 'kΩ' }
  },
  'Pressure': {
    good: { min: 980, max: 1020, unit: 'hPa' },
    moderate: { min: 960, max: 980, unit: 'hPa' },
    poor: { min: -Infinity, max: 960, unit: 'hPa' }
  },
  'Nitrogen Dioxide (NO2)': {
    good: { min: 0, max: 110, unit: 'ppb' },
    moderate: { min: 110, max: 130, unit: 'ppb' },
    hazardous: { min: 130, max: Infinity, unit: 'ppb' }
  },
  'Ozone (O3)': {
    good: { min: 0, max: 100, unit: 'ppb' },
    moderate: { min: 100, max: 120, unit: 'ppm' },
    hazardous: { min: 120, max: Infinity, unit: 'ppm' }
  }
};

// Generate Air Quality Standards for all locations
const airQualityStandards = {
  lectureHall: { ...baseAirQualityStandards },
  deansOffice: { ...baseAirQualityStandards },
  basement: { ...baseAirQualityStandards }
};

// No conversion needed - use raw sensor values
const convertSensorValue = (value: number, metric: string): number => {
  return value;
};

// Fetch sensor data for specific location and date
const fetchLocationDateData = async (location: string, date: string, metric: string) => {
  try {
    const apiLocation = locationMapping[location] || 'lecture-hall';
    const fieldName = metricFieldMap[metric];

    if (!fieldName) {
      throw new Error(`Metric "${metric}" is not supported`);
    }

    console.log(`🔍 Fetching data for ${apiLocation} on ${date} for ${metric}`);
    console.log(`🔍 API URL: ${SENSOR_DATA_API_BASE}/sensor-data/${apiLocation}/history?limit=10000`);

    const response = await fetch(`${SENSOR_DATA_API_BASE}/sensor-data/${apiLocation}/history?limit=10000`);

    if (!response.ok) {
      console.error(`❌ API request failed: ${response.status} ${response.statusText}`);
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log(`🔍 API Response:`, result);

    if (!result.success || !result.data) {
      console.log(`🔍 No data found for ${apiLocation}`);
      return [];
    }

    console.log(`🔍 Total readings from API: ${result.data.length}`);
    console.log(`🔍 Sample reading:`, result.data[0]);
    console.log(`🔍 Looking for date: "${date}"`);

    // Show available dates in the data
    const availableDates = result.data.map((reading: any) =>
      reading.timestamp ? reading.timestamp.substring(0, 10) : 'no-timestamp'
    ).filter((dateStr: string, index: number, arr: string[]) => arr.indexOf(dateStr) === index);
    console.log(`🔍 Available dates in data:`, availableDates);

    // Filter data for the specific date
    const dayReadings = result.data.filter((reading: any) => {
      const matches = reading.timestamp && reading.timestamp.startsWith(date);
      if (reading.timestamp) {
        console.log(`🔍 Timestamp: "${reading.timestamp}", Date: "${date}", Matches: ${matches}`);
      }
      return matches;
    });

    console.log(`🔍 Found ${dayReadings.length} readings for ${date}`);
    if (dayReadings.length > 0) {
      console.log(`🔍 Sample filtered reading:`, dayReadings[0]);
    }
    return dayReadings;

  } catch (error) {
    console.error(`❌ Error fetching data for ${location} on ${date}:`, error);
    return [];
  }
};

// Calculate daily average from readings
const calculateDailyAverage = (readings: any[], metric: string): number => {
  const fieldName = metricFieldMap[metric];

  if (!readings || readings.length === 0) {
    console.log(`🔍 No readings found for ${metric}, returning 0`);
    return 0;
  }

  console.log(`🔍 Processing ${readings.length} readings for ${metric} (field: ${fieldName})`);

  // Debug: Check first few raw values
  const rawValues = readings.slice(0, 3).map(r => r[fieldName]);
  console.log(`🔍 Sample raw ${fieldName} values:`, rawValues);

  const validValues = readings
    .map((reading: any) => {
      const value = reading[fieldName];
      console.log(`🔍 Raw value for ${fieldName}:`, value);
      return value;
    })
    .filter((value: any) => {
      const isValid = value !== undefined && value !== null && !isNaN(value) && value !== 0;
      console.log(`🔍 Value ${value} is valid: ${isValid}`);
      return isValid;
    })
    .map((value: number) => {
      const converted = convertSensorValue(value, metric);
      console.log(`🔍 Converted value: ${converted}`);
      return converted;
    });

  console.log(`🔍 Valid values for ${metric}:`, validValues.slice(0, 5));

  if (validValues.length === 0) {
    console.log(`🔍 No valid ${metric} values found, returning 0`);
    return 0;
  }

  const average = validValues.reduce((sum: number, val: number) => sum + val, 0) / validValues.length;
  const result = parseFloat(average.toFixed(2));
  console.log(`🔍 Calculated daily average for ${metric}: ${result} from ${validValues.length} readings`);

  // Additional validation
  if (isNaN(result)) {
    console.error(`❌ Calculated average is NaN for ${metric}`);
    return 0;
  }

  return result;
};

// Calculate hourly averages for chart
const calculateHourlyAverages = (readings: any[], metric: string): number[] => {
  const fieldName = metricFieldMap[metric];
  const hourlyData: number[] = new Array(24).fill(0);

  if (!readings || readings.length === 0) {
    console.log(`🔍 No readings for hourly calculation, returning all zeros`);
    return hourlyData;
  }

  console.log(`🔍 Processing ${readings.length} readings for hourly ${metric} calculation`);

  const hourlyReadings: { [hour: number]: number[] } = {};
  let processedCount = 0;

  readings.forEach((reading: any) => {
    if (reading.timestamp && reading[fieldName] !== undefined && reading[fieldName] !== null && !isNaN(reading[fieldName])) {
      try {
        let timestamp = reading.timestamp;
        if (timestamp.includes('_')) {
          // Convert Firebase timestamp format: 2025-07-15T06_21_43_199Z -> 2025-07-15T06:21:43.199Z
          timestamp = timestamp.replace(/T(\d{2})_(\d{2})_(\d{2})_(\d{3})Z/, 'T$1:$2:$3.$4Z');
        }

        const date = new Date(timestamp);
        if (!isNaN(date.getTime())) {
          const hour = date.getHours();

          if (hour >= 0 && hour < 24) {
            if (!hourlyReadings[hour]) {
              hourlyReadings[hour] = [];
            }
            const convertedValue = convertSensorValue(parseFloat(reading[fieldName]), metric);
            hourlyReadings[hour].push(convertedValue);
            processedCount++;
          }
        }
      } catch (error) {
        console.warn('Error parsing timestamp:', reading.timestamp, error);
      }
    }
  });

  console.log(`🔍 Processed ${processedCount} valid readings into hourly groups`);

  // Calculate averages for hours that have data
  let hoursWithData = 0;
  Object.keys(hourlyReadings).forEach(hourStr => {
    const hour = parseInt(hourStr);
    const values = hourlyReadings[hour];
    if (values.length > 0) {
      const average = values.reduce((sum, val) => sum + val, 0) / values.length;
      hourlyData[hour] = parseFloat(average.toFixed(2));
      hoursWithData++;
    }
  });

  console.log(`🔍 Final hourly data for ${metric}: ${hoursWithData} hours with data`);
  return hourlyData;
};

interface ChartData {
  location1: number[];
  location2: number[];
}

const Comparison: React.FC = () => {
  const [location1, setLocation1] = useState('');
  const [location2, setLocation2] = useState('');
  const [metric1, setMetric1] = useState('');
  const [metric2, setMetric2] = useState('');
  const [date1, setDate1] = useState('');
  const [date2, setDate2] = useState('');
  const [validationMessage, setValidationMessage] = useState('');
  const [chartData, setChartData] = useState<ChartData>({
    location1: new Array(24).fill(0),
    location2: new Array(24).fill(0)
  });
  
  const gauge1Ref = useRef<HTMLCanvasElement>(null);
  const gauge2Ref = useRef<HTMLCanvasElement>(null);
  const distributionChartRef = useRef<HTMLCanvasElement>(null);
  
  const gaugeChartsRef = useRef<{ [key: string]: Chart }>({});
  const distributionChartInstanceRef = useRef<Chart | null>(null);

  useEffect(() => {
    setupDatePickers();
    initializeGauges();
    initializeDistributionChart();

    // Add comparison-page class to body for background styling
    document.body.classList.add('comparison-page');

    return () => {
      // Cleanup charts
      Object.values(gaugeChartsRef.current).forEach(chart => chart.destroy());
      if (distributionChartInstanceRef.current) {
        distributionChartInstanceRef.current.destroy();
      }
      document.body.classList.remove('comparison-page');
    };
  }, []);

  useEffect(() => {
    updateData();
  }, [location1, location2, metric1, metric2, date1, date2]);

  useEffect(() => {
    // Update distribution chart when chart data changes
    console.log('🔍 useEffect triggered - chartData changed:', chartData);
    console.log('🔍 Location1 data:', chartData.location1);
    console.log('🔍 Location2 data:', chartData.location2);
    console.log('🔍 Location1 non-zero count:', chartData.location1.filter(v => v > 0).length);
    console.log('🔍 Location2 non-zero count:', chartData.location2.filter(v => v > 0).length);

    // Add a small delay to ensure chart is ready
    const timer = setTimeout(() => {
      if (distributionChartInstanceRef.current) {
        console.log('🔍 Calling updateDistributionChart from useEffect');
        updateDistributionChart();
      } else {
        console.log('⚠️ Chart instance not ready yet');
      }
    }, 200);

    return () => clearTimeout(timer);
  }, [chartData]);



  const setupDatePickers = () => {
    // Set default dates to current date for both locations
    const today = new Date().toISOString().split('T')[0];
    setDate1(today);
    setDate2(today);
  };



  const initializeGauges = () => {
    const config: ChartConfiguration = {
      type: 'doughnut',
      data: {
        datasets: [{
          data: [0, 100],
          backgroundColor: ['#28a745', '#e9ecef'],
          borderWidth: 0
        }]
      },
      options: {
        plugins: {
          tooltip: { enabled: false },
          legend: { display: false }
        },
        // Cast to any to handle Chart.js doughnut-specific options
        ...(({
          circumference: 180,
          rotation: 270,
          cutout: '70%'
        } as any)),
        responsive: true,
        maintainAspectRatio: false
      }
    };

    if (gauge1Ref.current) {
      const ctx1 = gauge1Ref.current.getContext('2d');
      if (ctx1) {
        gaugeChartsRef.current['gauge1'] = new Chart(ctx1, { ...config });
      }
    }

    if (gauge2Ref.current) {
      const ctx2 = gauge2Ref.current.getContext('2d');
      if (ctx2) {
        gaugeChartsRef.current['gauge2'] = new Chart(ctx2, { ...config });
      }
    }
  };

  const initializeDistributionChart = () => {
    if (distributionChartRef.current) {
      // Destroy existing chart if it exists
      if (distributionChartInstanceRef.current) {
        distributionChartInstanceRef.current.destroy();
        distributionChartInstanceRef.current = null;
      }

      const ctx = distributionChartRef.current.getContext('2d');
      if (ctx) {
        console.log('🔍 Initializing distribution chart');
        try {
          distributionChartInstanceRef.current = new Chart(ctx, {
            type: 'line',
            data: {
              labels: Array.from({length: 24}, (_, i) => `${i}:00`),
              datasets: [
                {
                  label: 'Location 1',
                  borderColor: '#007bff',
                  backgroundColor: 'rgba(0, 123, 255, 0.1)',
                  data: Array(24).fill(0),
                  tension: 0.4,
                  fill: false,
                  pointRadius: 3,
                  pointHoverRadius: 5
                },
                {
                  label: 'Location 2',
                  borderColor: '#28a745',
                  backgroundColor: 'rgba(40, 167, 69, 0.1)',
                  data: Array(24).fill(0),
                  tension: 0.4,
                  fill: false,
                  pointRadius: 3,
                  pointHoverRadius: 5
                }
              ]
            },
            options: {
              responsive: true,
              maintainAspectRatio: false,
              interaction: {
                intersect: false,
                mode: 'index'
              },
              plugins: {
                legend: {
                  display: true,
                  position: 'top'
                },
                tooltip: {
                  mode: 'index',
                  intersect: false
                }
              },
              scales: {
                x: {
                  display: true,
                  title: {
                    display: true,
                    text: 'Time (Hours)'
                  }
                },
                y: {
                  beginAtZero: true,
                  title: {
                    display: true,
                    text: 'Value'
                  }
                }
              }
            }
          });
          console.log('✅ Distribution chart initialized successfully');
        } catch (error) {
          console.error('❌ Error initializing chart:', error);
        }
      }
    }
  };

  const getQualityLevel = (value: number, standards: any) => {
    for (const level in standards) {
      const range = standards[level];
      if (value >= range.min && value < range.max) {
        return {
          level: level.replace(/\d+$/, ''),
          unit: range.unit
        };
      }
    }
    return {
      level: 'poor',
      unit: standards.good.unit
    };
  };

  const updateGauge = (index: number, value: number, location: string, metric: string) => {
    const standards = airQualityStandards[location as keyof typeof airQualityStandards][metric as keyof typeof baseAirQualityStandards];
    const qualityInfo = getQualityLevel(value, standards);
    const gaugeChart = gaugeChartsRef.current[`gauge${index}`];

    if (!gaugeChart) return;

    const colors = {
      good: '#28a745',
      moderate: '#ffc107',
      poor: '#dc3545'
    };

    // Use the same max value logic as dashboard
    const getMaxValue = (metric: string) => {
      const maxValues: { [key: string]: number } = {
        'AQI': 500,
        'Temperature': 40,
        'Humidity': 100,
        'PM2.5': 300,
        'PM10': 500,
        'CO2 Level': 2000,
        'Gas Resistance': 2000,
        'Pressure': 1050,
        'Nitrogen Dioxide (NO2)': 200,
        'Ozone (O3)': 200
      };
      return maxValues[metric] || 100;
    };

    const maxValue = getMaxValue(metric);
    const minValue = 0;
    const filledPercentage = Math.min(100, Math.max(0,
      ((value - minValue) / (maxValue - minValue)) * 100
    ));

    gaugeChart.data.datasets[0].backgroundColor = [
      colors[qualityInfo.level as keyof typeof colors],
      '#e9ecef'
    ];

    gaugeChart.data.datasets[0].data = [filledPercentage, 100 - filledPercentage];
    gaugeChart.update();
    
    // Update value display
    const valueElement = document.getElementById(`gauge${index}-value`);
    if (valueElement) {
      valueElement.textContent = `${value.toFixed(1)}${qualityInfo.unit}`;
    }
    
    // Update status
    const statusElement = document.getElementById(`status${index}`);
    if (statusElement) {
      statusElement.textContent = qualityInfo.level.charAt(0).toUpperCase() + qualityInfo.level.slice(1);
      statusElement.className = `status ${qualityInfo.level}`;
    }
  };

  const fetchRealData = async (location: string, metric: string, date: string, index: number) => {
    if (!location || !metric || !date) {
      const statusElement = document.getElementById(`status${index}`);
      if (statusElement) {
        statusElement.textContent = "Please select location, metric, and date";
        statusElement.className = 'status';
      }

      // Clear gauge value
      const valueElement = document.getElementById(`gauge${index}-value`);
      if (valueElement) {
        valueElement.textContent = '--';
      }
      return;
    }

    // Show loading status
    const statusElement = document.getElementById(`status${index}`);
    if (statusElement) {
      statusElement.textContent = "Loading data...";
      statusElement.className = 'status';
    }

    try {
      console.log(`🔍 Fetching real data for ${location} on ${date} for ${metric}`);
      console.log(`🔍 API URL will be: ${SENSOR_DATA_API_BASE}/sensor-data/${locationMapping[location] || 'lecture-hall'}/history?limit=10000`);

      // Fetch real data from Firebase
      const readings = await fetchLocationDateData(location, date, metric);

      if (readings.length === 0) {
        // Show error message asking user to reselect
        if (statusElement) {
          statusElement.textContent = `No data found for ${metric} in ${location} on ${date}. Please select different parameters.`;
          statusElement.className = 'status error';
        }

        // Clear gauge value
        const valueElement = document.getElementById(`gauge${index}-value`);
        if (valueElement) {
          valueElement.textContent = '--';
        }

        // Set chart data to all zeros
        setChartData(prev => ({
          ...prev,
          [`location${index}`]: new Array(24).fill(0)
        }));
        return;
      }

      // Calculate daily average
      const dailyAverage = calculateDailyAverage(readings, metric);
      console.log(`🔍 Daily average for ${location}: ${dailyAverage}`);

      // Validate daily average
      if (isNaN(dailyAverage) || dailyAverage === null || dailyAverage === undefined) {
        console.error(`❌ Invalid daily average: ${dailyAverage}`);
        if (statusElement) {
          statusElement.textContent = `Invalid data for ${metric} in ${location}`;
          statusElement.className = 'status error';
        }
        return;
      }

      // Calculate hourly averages for chart
      const hourlyData = calculateHourlyAverages(readings, metric);
      console.log(`🔍 Hourly data for ${location}:`, hourlyData.slice(0, 5));

      // Update gauge with real daily average
      updateGauge(index, dailyAverage, location, metric);

      // Update chart data
      setChartData(prev => {
        const newData = {
          ...prev,
          [`location${index}`]: hourlyData
        };
        console.log(`🔍 Setting chart data for location${index}:`, hourlyData.filter(h => h > 0));
        return newData;
      });

      console.log(`✅ Updated ${location} with daily average: ${dailyAverage} for ${metric}`);

      // Update comparison after data is loaded
      setTimeout(() => {
        updateComparison();
      }, 100);

    } catch (error) {
      console.error(`❌ Error fetching real data for ${location}:`, error);

      // Show error status
      if (statusElement) {
        statusElement.textContent = `Error loading data for ${location}. Please try again.`;
        statusElement.className = 'status error';
      }

      // Clear gauge value
      const valueElement = document.getElementById(`gauge${index}-value`);
      if (valueElement) {
        valueElement.textContent = '--';
      }

      // Set chart data to all zeros
      setChartData(prev => ({
        ...prev,
        [`location${index}`]: new Array(24).fill(0)
      }));
    }
  };

  const updateData = () => {
    console.log(`🔍 updateData called with: L1=${location1}, L2=${location2}, M1=${metric1}, M2=${metric2}, D1=${date1}, D2=${date2}`);

    // Check if all required fields are filled
    if (!location1 || !location2 || !date1 || !date2 || !metric1 || !metric2) {
      console.log('❌ Missing required fields, clearing charts');
      clearCharts();
      return;
    }

    // Check for same locations
    if (location1 === location2) {
      showError("Please select different locations for comparison");
      clearCharts();
      return;
    }

    // Check for different metrics
    if (metric1 !== metric2) {
      showError("Please select the same metric for both locations");
      clearCharts();
      return;
    }

    // Check for same dates - only show error if user has selected metrics (indicating they're trying to compare)
    if (date1 === date2 && metric1 && metric2) {
      showError("Please select different dates for comparison");
      clearCharts();
      return;
    }

    console.log('✅ Validation passed, fetching data...');
    hideError();
    fetchRealData(location1, metric1, date1, 1);
    fetchRealData(location2, metric2, date2, 2);
    // updateComparison will be called after data is loaded
  };

  const showError = (message: string) => {
    setValidationMessage(message);
    const comparisonText = document.getElementById('comparison-text');
    if (comparisonText) {
      comparisonText.textContent = message;
    }
  };

  const hideError = () => {
    setValidationMessage('');
  };

  const clearCharts = () => {
    const comparisonText = document.getElementById('comparison-text');
    if (comparisonText) {
      comparisonText.textContent = "Select valid comparison parameters to view summary";
    }

    // Clear gauge charts
    Object.values(gaugeChartsRef.current).forEach(gauge => {
      if (gauge) {
        gauge.data.datasets[0].backgroundColor = ['#e9ecef', '#e9ecef'];
        gauge.data.datasets[0].data = [0, 100];
        gauge.update();
      }
    });

    // Clear distribution chart
    if (distributionChartInstanceRef.current) {
      distributionChartInstanceRef.current.data.labels = [];
      distributionChartInstanceRef.current.data.datasets.forEach(dataset => {
        dataset.data = [];
      });
      distributionChartInstanceRef.current.update();
    }

    // Clear chart data state
    setChartData({ location1: new Array(24).fill(0), location2: new Array(24).fill(0) });

    // Clear gauge values
    const gauge1Value = document.getElementById('gauge1-value');
    const gauge2Value = document.getElementById('gauge2-value');
    if (gauge1Value) gauge1Value.textContent = '--';
    if (gauge2Value) gauge2Value.textContent = '--';

    // Reset status indicators
    const status1 = document.getElementById('status1');
    const status2 = document.getElementById('status2');
    if (status1) {
      status1.textContent = 'Select location to view data';
      status1.className = 'status';
    }
    if (status2) {
      status2.textContent = 'Select location to view data';
      status2.className = 'status';
    }
  };

  const updateComparison = () => {
    if (!location1 || !location2 || !metric1 || !metric2) {
      const comparisonText = document.getElementById('comparison-text');
      if (comparisonText) {
        comparisonText.textContent = "Select both locations and metrics to view comparison";
      }
      return;
    }

    const value1Element = document.getElementById('gauge1-value');
    const value2Element = document.getElementById('gauge2-value');
    const status1Element = document.getElementById('status1');
    const status2Element = document.getElementById('status2');

    if (!value1Element || !value2Element || !status1Element || !status2Element) {
      console.log('❌ Missing comparison elements');
      return;
    }

    // Extract numeric values from gauge displays
    const value1Text = value1Element.textContent || '--';
    const value2Text = value2Element.textContent || '--';

    console.log(`🔍 Gauge values - Location 1: "${value1Text}", Location 2: "${value2Text}"`);

    // Handle different formats (e.g., "25.3°C", "43.9 kΩ", "--")
    let value1 = 0;
    let value2 = 0;

    if (value1Text !== '--') {
      const match1 = value1Text.match(/^([\d.]+)/);
      value1 = match1 ? parseFloat(match1[1]) : 0;
      console.log(`🔍 Extracted value1: ${value1} from "${value1Text}"`);
    }

    if (value2Text !== '--') {
      const match2 = value2Text.match(/^([\d.]+)/);
      value2 = match2 ? parseFloat(match2[1]) : 0;
      console.log(`🔍 Extracted value2: ${value2} from "${value2Text}"`);
    }

    const quality1 = status1Element.textContent?.toLowerCase() || 'unknown';
    const quality2 = status2Element.textContent?.toLowerCase() || 'unknown';

    console.log(`🔍 Comparison values: ${value1} (${quality1}) vs ${value2} (${quality2})`);

    // Validate values
    if (isNaN(value1) || isNaN(value2)) {
      const comparisonTextElement = document.getElementById('comparison-text');
      if (comparisonTextElement) {
        comparisonTextElement.textContent = "Waiting for data to load...";
      }
      return;
    }

    try {
      const standards1 = airQualityStandards[location1 as keyof typeof airQualityStandards][metric1 as keyof typeof baseAirQualityStandards];
      const standards2 = airQualityStandards[location2 as keyof typeof airQualityStandards][metric2 as keyof typeof baseAirQualityStandards];

      // Get proper unit display
      let unit1 = standards1.good.unit;
      let unit2 = standards2.good.unit;

      const comparisonText = `${location1} shows ${value1.toFixed(1)}${unit1} for ${metric1} (${quality1}), while ${location2} shows ${value2.toFixed(1)}${unit2} for ${metric2} (${quality2}).`;

      const comparisonTextElement = document.getElementById('comparison-text');
      if (comparisonTextElement) {
        comparisonTextElement.textContent = comparisonText;
      }

      console.log('✅ Comparison updated successfully');

    } catch (error) {
      console.error('❌ Error updating comparison:', error);
      const comparisonTextElement = document.getElementById('comparison-text');
      if (comparisonTextElement) {
        comparisonTextElement.textContent = "Error generating comparison";
      }
    }
  };

  const updateDistributionChart = () => {
    console.log('🔍 updateDistributionChart called');
    console.log('🔍 Current chartData:', chartData);

    if (!distributionChartInstanceRef.current) {
      console.log('❌ No chart instance available');
      return;
    }

    try {
      // Always update the chart with current data
      distributionChartInstanceRef.current.data.labels = Array.from({length: 24}, (_, i) => `${i}:00`);

      // Update location 1 data
      if (chartData.location1 && chartData.location1.length === 24) {
        distributionChartInstanceRef.current.data.datasets[0].data = [...chartData.location1];
        distributionChartInstanceRef.current.data.datasets[0].label = location1 || 'Location 1';
        const nonZeroCount = chartData.location1.filter(v => v > 0).length;
        console.log(`✅ Updated location1 chart with ${nonZeroCount} non-zero values`);
      } else {
        distributionChartInstanceRef.current.data.datasets[0].data = Array(24).fill(0);
        distributionChartInstanceRef.current.data.datasets[0].label = 'Location 1';
      }

      // Update location 2 data
      if (chartData.location2 && chartData.location2.length === 24) {
        distributionChartInstanceRef.current.data.datasets[1].data = [...chartData.location2];
        distributionChartInstanceRef.current.data.datasets[1].label = location2 || 'Location 2';
        const nonZeroCount = chartData.location2.filter(v => v > 0).length;
        console.log(`✅ Updated location2 chart with ${nonZeroCount} non-zero values`);
      } else {
        distributionChartInstanceRef.current.data.datasets[1].data = Array(24).fill(0);
        distributionChartInstanceRef.current.data.datasets[1].label = 'Location 2';
      }

      distributionChartInstanceRef.current.update('none'); // Use 'none' for immediate update
      console.log('✅ Chart updated successfully');

    } catch (error) {
      console.error('❌ Error updating chart:', error);
    }
  };

  return (
    <Layout showNavbar={true}>
      <header className="welcome-section">
        <h1>Welcome to the Smart Indoor Air Quality Monitoring System</h1>
        <p className="subtitle">Track, analyze, and improve indoor air quality with ease</p>
      </header>

      <main className="container">
        <h2>Select your Locations</h2>
        
        <div id="validation-message" className="validation-message" style={{
          display: validationMessage ? 'block' : 'none'
        }}>
          {validationMessage}
        </div>

        <div className="comparison-grid">
          {/* Location 1 */}
          <div className="location-card">
            <h3>Location 1</h3>
            <select
              id="location1"
              className="location-select"
              value={location1}
              onChange={(e) => setLocation1(e.target.value)}
            >
              <option value="">Select Location</option>
              <option value="lectureHall">Lecture Hall</option>
              <option value="deansOffice">Dean's Office</option>
              <option value="basement">Basement</option>
            </select>
            
            <div className="date-picker">
              <label>Date:</label>
              <input
                type="date"
                id="date1"
                value={date1}
                onChange={(e) => setDate1(e.target.value)}
              />
            </div>

            <div className="metrics-select">
              <label>Metrics:</label>
              <select
                id="metrics1"
                value={metric1}
                onChange={(e) => setMetric1(e.target.value)}
              >
                <option value="">Select Metric</option>
                <option value="Temperature">Temperature</option>
                <option value="Humidity">Humidity</option>
                <option value="PM2.5">PM2.5</option>
                <option value="PM10">PM10</option>
                <option value="CO2 Level">Carbon Dioxide CO₂</option>
                <option value="Gas Resistance">Gas Resistance</option>
                <option value="Pressure">Pressure</option>
                <option value="Nitrogen Dioxide (NO2)">Nitrogen Dioxide (NO2)</option>
                <option value="Ozone (O3)">Ozone (O3)</option>
              </select>
            </div>

            <div className="gauge-container">
              <canvas ref={gauge1Ref} id="gauge1" width="200" height="200"></canvas>
              <div id="gauge1-value" className="value-display">--</div>
            </div>

            <div id="status1" className="status">
              Select location to view data
            </div>

            <div className="legend">
              <span className="legend-item">
                <span className="dot good"></span>
                Good
              </span>
              <span className="legend-item">
                <span className="dot moderate"></span>
                Moderate
              </span>
              <span className="legend-item">
                <span className="dot poor"></span>
                Poor
              </span>
            </div>
          </div>

          {/* Location 2 */}
          <div className="location-card">
            <h3>Location 2</h3>
            <select
              id="location2"
              className="location-select"
              value={location2}
              onChange={(e) => setLocation2(e.target.value)}
            >
              <option value="">Select Location</option>
              <option value="lectureHall">Lecture Hall</option>
              <option value="deansOffice">Dean's Office</option>
              <option value="basement">Basement</option>
            </select>

            <div className="date-picker">
              <label>Date:</label>
              <input
                type="date"
                id="date2"
                value={date2}
                onChange={(e) => setDate2(e.target.value)}
              />
            </div>

            <div className="metrics-select">
              <label>Metrics:</label>
              <select
                id="metrics2"
                value={metric2}
                onChange={(e) => setMetric2(e.target.value)}
              >
                <option value="">Select Metric</option>
                <option value="Temperature">Temperature</option>
                <option value="Humidity">Humidity</option>
                <option value="PM2.5">PM2.5</option>
                <option value="PM10">PM10</option>
                <option value="CO2 Level">Carbon Dioxide CO₂</option>
                <option value="Gas Resistance">Gas Resistance</option>
                <option value="Pressure">Pressure</option>
                <option value="Nitrogen Dioxide (NO2)">Nitrogen Dioxide (NO2)</option>
                <option value="Ozone (O3)">Ozone (O3)</option>
              </select>
            </div>

            <div className="gauge-container">
              <canvas ref={gauge2Ref} id="gauge2" width="200" height="200"></canvas>
              <div id="gauge2-value" className="value-display">--</div>
            </div>

            <div id="status2" className="status">
              Select location to view data
            </div>

            <div className="legend">
              <span className="legend-item">
                <span className="dot good"></span>
                Good
              </span>
              <span className="legend-item">
                <span className="dot moderate"></span>
                Moderate
              </span>
              <span className="legend-item">
                <span className="dot poor"></span>
                Poor
              </span>
            </div>
          </div>
        </div>

        <div id="chart-error" className="chart-error" style={{ display: 'none' }}></div>

        <div className="comparison-summary">
          <h3>Comparison Summary</h3>
          <div id="comparison-text" className="comparison-text">
            Select locations and metrics to view comparison
          </div>
        </div>

        <div className="daily-distribution">
          <h3>Daily Distribution</h3>
          <div className="chart-container">
            <canvas ref={distributionChartRef} id="distributionChart"></canvas>
          </div>
        </div>


      </main>
    </Layout>
  );
};

export default Comparison;
