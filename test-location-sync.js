#!/usr/bin/env node

/**
 * Location Synchronization Test Script
 * 
 * This script tests the location synchronization between the location service
 * and multiple simulated clients to ensure proper WebSocket broadcasting.
 */

const WebSocket = require('ws');
const fetch = require('node-fetch');

const LOCATION_SERVICE_URL = 'http://localhost:3002';
const WEBSOCKET_URL = 'ws://localhost:3002';

class LocationSyncTester {
  constructor() {
    this.clients = [];
    this.testResults = [];
  }

  async runTests() {
    console.log('🧪 Starting Location Synchronization Tests...\n');

    try {
      // Test 1: Check if location service is running
      await this.testServiceHealth();

      // Test 2: Create multiple WebSocket clients
      await this.createTestClients(3);

      // Test 3: Test location update broadcasting
      await this.testLocationBroadcast();

      // Test 4: Test location persistence
      await this.testLocationPersistence();

      // Summary
      this.printTestSummary();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
    } finally {
      this.cleanup();
    }
  }

  async testServiceHealth() {
    console.log('🔍 Test 1: Checking location service health...');
    
    try {
      const response = await fetch(`${LOCATION_SERVICE_URL}/health`);
      const data = await response.json();
      
      if (response.ok && data.status === 'healthy') {
        console.log('✅ Location service is healthy');
        console.log(`   Connected clients: ${data.connectedClients}`);
        this.testResults.push({ test: 'Service Health', status: 'PASS' });
      } else {
        throw new Error('Service not healthy');
      }
    } catch (error) {
      console.log('❌ Location service health check failed:', error.message);
      this.testResults.push({ test: 'Service Health', status: 'FAIL', error: error.message });
      throw error;
    }
    console.log('');
  }

  async createTestClients(count) {
    console.log(`🔗 Test 2: Creating ${count} WebSocket test clients...`);

    for (let i = 0; i < count; i++) {
      const client = await this.createWebSocketClient(`Client-${i + 1}`);
      this.clients.push(client);
      await this.sleep(500); // Small delay between connections
    }

    console.log(`✅ Created ${this.clients.length} WebSocket clients`);
    this.testResults.push({ test: 'WebSocket Clients', status: 'PASS' });
    console.log('');
  }

  createWebSocketClient(name) {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(WEBSOCKET_URL);
      const client = {
        name,
        ws,
        messagesReceived: [],
        connected: false
      };

      ws.on('open', () => {
        console.log(`   🔌 ${name} connected`);
        client.connected = true;
        resolve(client);
      });

      ws.on('message', (data) => {
        try {
          const message = JSON.parse(data);
          console.log(`   📨 ${name} received:`, message);
          client.messagesReceived.push(message);
        } catch (error) {
          console.error(`   ❌ ${name} message parse error:`, error);
        }
      });

      ws.on('close', () => {
        console.log(`   🔌 ${name} disconnected`);
        client.connected = false;
      });

      ws.on('error', (error) => {
        console.error(`   ❌ ${name} WebSocket error:`, error);
        reject(error);
      });

      // Timeout after 5 seconds
      setTimeout(() => {
        if (!client.connected) {
          reject(new Error(`${name} connection timeout`));
        }
      }, 5000);
    });
  }

  async testLocationBroadcast() {
    console.log('📡 Test 3: Testing location update broadcasting...');

    const testLocation = 'lecture-hall';
    
    try {
      // Clear previous messages
      this.clients.forEach(client => {
        client.messagesReceived = [];
      });

      // Send location update
      console.log(`   📍 Sending location update: ${testLocation}`);
      const response = await fetch(`${LOCATION_SERVICE_URL}/api/location`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          location: testLocation,
          isAdmin: true,
          adminId: 'test-admin'
        })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('   ✅ Location update sent successfully:', result);

      // Wait for WebSocket messages
      await this.sleep(2000);

      // Check if all clients received the update
      let allReceived = true;
      this.clients.forEach(client => {
        const locationUpdates = client.messagesReceived.filter(
          msg => msg.type === 'location-update' && msg.data.currentLocation === testLocation
        );
        
        if (locationUpdates.length === 0) {
          console.log(`   ❌ ${client.name} did not receive location update`);
          allReceived = false;
        } else {
          console.log(`   ✅ ${client.name} received location update`);
        }
      });

      if (allReceived) {
        console.log('✅ All clients received location update');
        this.testResults.push({ test: 'Location Broadcast', status: 'PASS' });
      } else {
        throw new Error('Not all clients received location update');
      }

    } catch (error) {
      console.log('❌ Location broadcast test failed:', error.message);
      this.testResults.push({ test: 'Location Broadcast', status: 'FAIL', error: error.message });
    }
    console.log('');
  }

  async testLocationPersistence() {
    console.log('💾 Test 4: Testing location persistence...');

    try {
      // Get current location
      const response = await fetch(`${LOCATION_SERVICE_URL}/api/location`);
      const data = await response.json();

      if (response.ok && data.success) {
        console.log('   ✅ Location retrieved successfully:', data.location);
        this.testResults.push({ test: 'Location Persistence', status: 'PASS' });
      } else {
        throw new Error('Failed to retrieve location');
      }

    } catch (error) {
      console.log('❌ Location persistence test failed:', error.message);
      this.testResults.push({ test: 'Location Persistence', status: 'FAIL', error: error.message });
    }
    console.log('');
  }

  printTestSummary() {
    console.log('📊 Test Summary:');
    console.log('================');
    
    let passed = 0;
    let failed = 0;

    this.testResults.forEach(result => {
      const status = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${status} ${result.test}: ${result.status}`);
      if (result.error) {
        console.log(`   Error: ${result.error}`);
      }
      
      if (result.status === 'PASS') passed++;
      else failed++;
    });

    console.log('');
    console.log(`Total: ${this.testResults.length} tests`);
    console.log(`Passed: ${passed}`);
    console.log(`Failed: ${failed}`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Location synchronization is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Check the location service and WebSocket connections.');
    }
  }

  cleanup() {
    console.log('\n🧹 Cleaning up test clients...');
    this.clients.forEach(client => {
      if (client.ws && client.ws.readyState === WebSocket.OPEN) {
        client.ws.close();
      }
    });
    console.log('✅ Cleanup completed');
  }

  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new LocationSyncTester();
  tester.runTests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = LocationSyncTester;
