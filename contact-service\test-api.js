/**
 * API Testing Script for Contact Service
 * Tests the contact form submission endpoint
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3005';

// Test data
const validContactData = {
  name: '<PERSON>',
  email: '<EMAIL>',
  subject: 'Test Contact Form Submission',
  message: 'This is a test message to verify that the contact form backend is working correctly.'
};

const invalidContactData = {
  name: 'A', // Too short
  email: 'invalid-email', // Invalid format
  subject: 'Hi', // Too short
  message: 'Short' // Too short
};

async function testHealthEndpoint() {
  console.log('\n🔍 Testing Health Endpoint...');
  try {
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testValidContactSubmission() {
  console.log('\n📧 Testing Valid Contact Form Submission...');
  try {
    const response = await axios.post(`${BASE_URL}/api/contact`, validContactData);
    console.log('✅ Valid submission successful:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Valid submission failed:', error.response?.data || error.message);
    return false;
  }
}

async function testInvalidContactSubmission() {
  console.log('\n❌ Testing Invalid Contact Form Submission...');
  try {
    const response = await axios.post(`${BASE_URL}/api/contact`, invalidContactData);
    console.log('❌ Invalid submission should have failed but succeeded:', response.data);
    return false;
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Invalid submission correctly rejected:', error.response.data);
      return true;
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testMissingFieldsSubmission() {
  console.log('\n🚫 Testing Missing Fields Submission...');
  try {
    const response = await axios.post(`${BASE_URL}/api/contact`, { name: 'John Doe' });
    console.log('❌ Missing fields submission should have failed but succeeded:', response.data);
    return false;
  } catch (error) {
    if (error.response?.status === 400) {
      console.log('✅ Missing fields correctly rejected:', error.response.data);
      return true;
    } else {
      console.error('❌ Unexpected error:', error.response?.data || error.message);
      return false;
    }
  }
}

async function testRateLimiting() {
  console.log('\n⏱️ Testing Rate Limiting (sending multiple requests)...');
  const promises = [];
  
  // Send 12 requests quickly (limit is 10 per 15 minutes)
  for (let i = 0; i < 12; i++) {
    promises.push(
      axios.post(`${BASE_URL}/api/contact`, {
        ...validContactData,
        subject: `Rate Limit Test ${i + 1}`
      }).catch(error => error.response)
    );
  }
  
  try {
    const responses = await Promise.all(promises);
    const successful = responses.filter(r => r.status === 200).length;
    const rateLimited = responses.filter(r => r.status === 429).length;
    
    console.log(`📊 Results: ${successful} successful, ${rateLimited} rate limited`);
    
    if (rateLimited > 0) {
      console.log('✅ Rate limiting is working correctly');
      return true;
    } else {
      console.log('⚠️ Rate limiting may not be working as expected');
      return false;
    }
  } catch (error) {
    console.error('❌ Rate limiting test failed:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Contact Service API Tests...');
  console.log('=' .repeat(50));
  
  const results = [];
  
  // Test health endpoint
  results.push(await testHealthEndpoint());
  
  // Test valid submission
  results.push(await testValidContactSubmission());
  
  // Test invalid submission
  results.push(await testInvalidContactSubmission());
  
  // Test missing fields
  results.push(await testMissingFieldsSubmission());
  
  // Test rate limiting (optional - may affect other tests)
  // results.push(await testRateLimiting());
  
  console.log('\n' + '=' .repeat(50));
  console.log('📊 Test Results Summary:');
  console.log(`✅ Passed: ${results.filter(r => r).length}`);
  console.log(`❌ Failed: ${results.filter(r => !r).length}`);
  console.log(`📈 Success Rate: ${Math.round((results.filter(r => r).length / results.length) * 100)}%`);
  
  if (results.every(r => r)) {
    console.log('\n🎉 All tests passed! Contact service is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Please check the service configuration.');
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test execution failed:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testHealthEndpoint,
  testValidContactSubmission,
  testInvalidContactSubmission,
  testMissingFieldsSubmission,
  testRateLimiting,
  runAllTests
};
