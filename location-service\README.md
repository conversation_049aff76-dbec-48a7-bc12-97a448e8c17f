# AirSense Location Synchronization Service

A lightweight Node.js service that enables multi-machine location synchronization for the AirSense application.

## 🎯 Purpose

This service solves the problem of location synchronization across multiple machines:
- **Before**: Each machine had its own localStorage, no sync between machines
- **After**: All machines share the same admin-selected location in real-time

## 🚀 Features

- **Multi-machine sync**: Location changes appear on all connected machines instantly
- **Real-time updates**: WebSocket connections for immediate synchronization
- **Persistent storage**: Location persists until admin changes it again
- **Admin-only control**: Only authenticated admins can change locations
- **Graceful fallback**: Main app works without this service (uses localStorage)
- **Lightweight**: Simple JSON file storage, no database required

## 📡 API Endpoints

### GET /api/location
Get the current global location
```json
{
  "success": true,
  "location": "deans-office",
  "lastUpdated": "2024-01-15T10:30:00.000Z",
  "updatedBy": "admin"
}
```

### POST /api/location
Set new location (admin only)
```json
{
  "location": "lecture-hall",
  "isAdmin": true,
  "adminId": "<EMAIL>"
}
```

### GET /health
Service health check
```json
{
  "status": "healthy",
  "service": "airsense-location-service",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "connectedClients": 3
}
```

## 🔌 WebSocket Events

### Connection
- Automatically sends current location to new clients
- Maintains persistent connection for real-time updates

### location-update Event
```json
{
  "type": "location-update",
  "data": {
    "currentLocation": "basement",
    "lastUpdated": "2024-01-15T10:30:00.000Z",
    "updatedBy": "admin"
  }
}
```

## 🛠️ Setup & Installation

### 1. Install Dependencies
```bash
cd location-service
npm install
```

### 2. Start Service
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

### 3. Service URLs
- **HTTP API**: http://localhost:3002
- **WebSocket**: ws://localhost:3002
- **Health Check**: http://localhost:3002/health

## 📁 File Structure

```
location-service/
├── server.js              # Main server file
├── package.json           # Dependencies and scripts
├── location-data.json     # Location storage (auto-created)
└── README.md              # This file
```

## 🔧 Configuration

### Environment Variables
- `PORT`: Service port (default: 3002)

### CORS Origins
The service accepts requests from:
- http://localhost:5173-5177 (Main app - multiple ports for flexibility)
- http://localhost:3001, 3003, 3004 (Admin app - multiple ports for flexibility)
- Automatically handles port conflicts by supporting multiple port ranges

## 🔄 How It Works

1. **Admin changes location** in admin app
2. **Admin app sends** location to this service via POST /api/location
3. **Service stores** location in JSON file
4. **Service broadcasts** update to all connected machines via WebSocket
5. **All machines receive** real-time location update
6. **Location persists** until next admin change

## 🛡️ Security

- **Admin validation**: Only requests with `isAdmin: true` can change location
- **CORS protection**: Only allowed origins can access the service
- **Input validation**: Location values are validated against allowed options

## 📊 Monitoring

- **Health endpoint**: Check service status and connected clients
- **Console logging**: All operations are logged with emojis for easy reading
- **Error handling**: Graceful error handling with proper HTTP status codes

## 🔧 Integration with Main App

The main app will:
1. **Check this service first** for location
2. **Fall back to localStorage** if service is unavailable
3. **Listen for WebSocket updates** for real-time sync
4. **Send location changes** when admin updates location

This ensures the main app works with or without this service!
