import { useState, useEffect, useCallback } from 'react';
import sensorDataService from '../services/sensorDataService';

// Custom hook for managing sensor data
export const useSensorData = (location = null) => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Fetch latest data
  const fetchLatestData = useCallback(async () => {
    if (!location) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const latestData = await sensorDataService.getLatestSensorData(location);
      const formattedData = sensorDataService.formatSensorData(latestData);
      
      setData(formattedData);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err.message);
      console.error(`Error fetching sensor data for ${location}:`, err);
    } finally {
      setLoading(false);
    }
  }, [location]);

  // Handle real-time updates
  const handleRealtimeUpdate = useCallback((updateLocation, updateData) => {
    if (!location || updateLocation === location) {
      const formattedData = sensorDataService.formatSensorData(updateData);
      setData(formattedData);
      setLastUpdated(new Date());
      setError(null);
    }
  }, [location]);

  // Initialize data and real-time updates
  useEffect(() => {
    if (location) {
      fetchLatestData();
      
      // Add real-time listener
      sensorDataService.addListener(handleRealtimeUpdate);
      
      return () => {
        sensorDataService.removeListener(handleRealtimeUpdate);
      };
    }
  }, [location, fetchLatestData, handleRealtimeUpdate]);

  return {
    data,
    loading,
    error,
    lastUpdated,
    refetch: fetchLatestData
  };
};

// Hook for all locations data
export const useAllSensorData = () => {
  const [data, setData] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [lastUpdated, setLastUpdated] = useState(null);

  // Fetch all locations data
  const fetchAllData = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const allData = await sensorDataService.getAllLocationData();
      const formattedData = {};
      
      Object.entries(allData).forEach(([location, locationData]) => {
        formattedData[location] = sensorDataService.formatSensorData(locationData);
      });
      
      setData(formattedData);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err.message);
      console.error('Error fetching all sensor data:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle real-time updates for all locations
  const handleRealtimeUpdate = useCallback((updateLocation, updateData) => {
    const formattedData = sensorDataService.formatSensorData(updateData);
    
    setData(prevData => ({
      ...prevData,
      [updateLocation]: formattedData
    }));
    setLastUpdated(new Date());
    setError(null);
  }, []);

  // Initialize data and real-time updates
  useEffect(() => {
    fetchAllData();
    
    // Add real-time listener
    sensorDataService.addListener(handleRealtimeUpdate);
    
    return () => {
      sensorDataService.removeListener(handleRealtimeUpdate);
    };
  }, [fetchAllData, handleRealtimeUpdate]);

  return {
    data,
    loading,
    error,
    lastUpdated,
    refetch: fetchAllData
  };
};

// Hook for historical data
export const useHistoricalData = (location, limit = 100) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchHistoricalData = useCallback(async () => {
    if (!location) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const historicalData = await sensorDataService.getHistoricalSensorData(location, limit);
      const formattedData = historicalData.map(item => 
        sensorDataService.formatSensorData(item)
      );
      
      setData(formattedData);
    } catch (err) {
      setError(err.message);
      console.error(`Error fetching historical data for ${location}:`, err);
    } finally {
      setLoading(false);
    }
  }, [location, limit]);

  useEffect(() => {
    fetchHistoricalData();
  }, [fetchHistoricalData]);

  return {
    data,
    loading,
    error,
    refetch: fetchHistoricalData
  };
};

// Hook for daily averages
export const useDailyAverages = (location, days = 30) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchDailyAverages = useCallback(async () => {
    if (!location) return;
    
    try {
      setLoading(true);
      setError(null);
      
      const dailyData = await sensorDataService.getDailyAverages(location, days);
      setData(dailyData);
    } catch (err) {
      setError(err.message);
      console.error(`Error fetching daily averages for ${location}:`, err);
    } finally {
      setLoading(false);
    }
  }, [location, days]);

  useEffect(() => {
    fetchDailyAverages();
  }, [fetchDailyAverages]);

  return {
    data,
    loading,
    error,
    refetch: fetchDailyAverages
  };
};

// Hook for service health
export const useServiceHealth = () => {
  const [health, setHealth] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const checkHealth = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      
      const healthData = await sensorDataService.checkHealth();
      setHealth(healthData);
    } catch (err) {
      setError(err.message);
      console.error('Error checking service health:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    checkHealth();

    // REMOVED: Excessive health check polling - check only on mount
    // Health can be checked manually if needed
  }, []);

  return {
    health,
    loading,
    error,
    refetch: checkHealth
  };
};
