const moment = require('moment');
const { FirebaseService } = require('./firebase');

// Daily average calculation service
class DailyAverageService {
  constructor() {
    this.isRunning = false;
    this.intervalId = null;
  }

  // Calculate daily averages for all locations
  async calculateDailyAverages(targetDate = null) {
    try {
      const date = targetDate || moment().subtract(1, 'day').format('YYYY-MM-DD');
      const locations = ['deans-office', 'lecture-hall', 'basement'];
      
      console.log(`📊 Calculating daily averages for ${date}...`);
      
      for (const location of locations) {
        await this.calculateLocationDailyAverage(location, date);
      }
      
      console.log(`✅ Daily averages calculation completed for ${date}`);
    } catch (error) {
      console.error('❌ Error calculating daily averages:', error);
      throw error;
    }
  }

  // Calculate daily average for a specific location
  async calculateLocationDailyAverage(location, date) {
    try {
      // Get all sensor data for the specified date
      const startOfDay = moment(date).startOf('day').toISOString();
      const endOfDay = moment(date).endOf('day').toISOString();
      
      // Read all sensor data for the location
      const allData = await FirebaseService.readSensorData(location, 1000);
      
      // Filter data for the specific date
      const dayData = allData.filter(reading => {
        // Fix timestamp parsing - handle underscore format
        let readingTimestamp = reading.timestamp;
        
        // If timestamp contains underscores, convert them back to proper format
        if (readingTimestamp && readingTimestamp.includes('_')) {
          readingTimestamp = readingTimestamp.replace(/_/g, ':');
        }
        
        const readingDate = moment(readingTimestamp);
        return readingDate.isValid() && readingDate.isBetween(startOfDay, endOfDay, null, '[]');
      });
      
      if (dayData.length === 0) {
        console.log(`⚠️ No data found for ${location} on ${date}`);
        return null;
      }
      
      // Calculate averages
      const averages = this.calculateAverages(dayData);
      
      // Add metadata
      const dailyAverage = {
        ...averages,
        date: date,
        location: location,
        dataPointsCount: dayData.length,
        calculatedAt: new Date().toISOString(),
        firstReading: dayData[dayData.length - 1].timestamp, // oldest
        lastReading: dayData[0].timestamp // newest
      };
      
      // Store in Firebase
      await FirebaseService.writeDailyAverage(location, date, dailyAverage);
      
      console.log(`📈 Daily average calculated for ${location} on ${date}:`, {
        dataPoints: dayData.length,
        avgTemp: averages.avgTemperature.toFixed(1),
        avgHumidity: averages.avgHumidity.toFixed(1),
        avgCO2: averages.avgCO2.toFixed(0),
        avgPM25: averages.avgPM25.toFixed(1),
        avgAQI: averages.avgAQI.toFixed(0)
      });
      
      return dailyAverage;
    } catch (error) {
      console.error(`❌ Error calculating daily average for ${location} on ${date}:`, error);
      throw error;
    }
  }

  // Calculate statistical averages from sensor readings
  calculateAverages(readings) {
    const metrics = {
      temperature: [],
      humidity: [],
      co2: [],
      pm25: [],
      pm10: [],
      voc: [],
      aqi: []
    };
    
    // Collect all values
    readings.forEach(reading => {
      if (reading.temperature !== undefined) metrics.temperature.push(reading.temperature);
      if (reading.humidity !== undefined) metrics.humidity.push(reading.humidity);
      if (reading.co2 !== undefined) metrics.co2.push(reading.co2);
      if (reading.pm25 !== undefined) metrics.pm25.push(reading.pm25);
      if (reading.pm10 !== undefined) metrics.pm10.push(reading.pm10);
      if (reading.voc !== undefined) metrics.voc.push(reading.voc);
      if (reading.aqi !== undefined) metrics.aqi.push(reading.aqi);
    });
    
    // Calculate statistics for each metric
    const calculateStats = (values) => {
      if (values.length === 0) return { avg: 0, min: 0, max: 0 };
      
      const sorted = values.sort((a, b) => a - b);
      return {
        avg: values.reduce((sum, val) => sum + val, 0) / values.length,
        min: sorted[0],
        max: sorted[sorted.length - 1],
        median: sorted[Math.floor(sorted.length / 2)]
      };
    };
    
    return {
      // Average values (primary metrics)
      avgTemperature: calculateStats(metrics.temperature).avg,
      avgHumidity: calculateStats(metrics.humidity).avg,
      avgCO2: calculateStats(metrics.co2).avg,
      avgPM25: calculateStats(metrics.pm25).avg,
      avgPM10: calculateStats(metrics.pm10).avg,
      avgVOC: calculateStats(metrics.voc).avg,
      avgAQI: calculateStats(metrics.aqi).avg,
      
      // Min/Max values for analysis
      minTemperature: calculateStats(metrics.temperature).min,
      maxTemperature: calculateStats(metrics.temperature).max,
      minHumidity: calculateStats(metrics.humidity).min,
      maxHumidity: calculateStats(metrics.humidity).max,
      minCO2: calculateStats(metrics.co2).min,
      maxCO2: calculateStats(metrics.co2).max,
      minPM25: calculateStats(metrics.pm25).min,
      maxPM25: calculateStats(metrics.pm25).max,
      minAQI: calculateStats(metrics.aqi).min,
      maxAQI: calculateStats(metrics.aqi).max,
      
      // Median values for better representation
      medianTemperature: calculateStats(metrics.temperature).median,
      medianHumidity: calculateStats(metrics.humidity).median,
      medianCO2: calculateStats(metrics.co2).median,
      medianPM25: calculateStats(metrics.pm25).median,
      medianAQI: calculateStats(metrics.aqi).median
    };
  }

  // Start automatic daily average calculation (runs at midnight)
  startAutomaticCalculation() {
    if (this.isRunning) {
      console.log('⚠️ Daily average calculation is already running');
      return;
    }
    
    this.isRunning = true;
    
    // Calculate immediately for yesterday if not done
    this.calculateDailyAverages().catch(error => {
      console.error('❌ Initial daily average calculation failed:', error);
    });
    
    // Set up daily calculation at 00:30 (30 minutes after midnight)
    const scheduleNextCalculation = () => {
      const now = moment();
      const nextRun = moment().add(1, 'day').startOf('day').add(30, 'minutes');
      const msUntilNextRun = nextRun.diff(now);
      
      console.log(`⏰ Next daily average calculation scheduled for: ${nextRun.format('YYYY-MM-DD HH:mm:ss')}`);
      
      this.intervalId = setTimeout(() => {
        this.calculateDailyAverages().then(() => {
          scheduleNextCalculation(); // Schedule next run
        }).catch(error => {
          console.error('❌ Scheduled daily average calculation failed:', error);
          scheduleNextCalculation(); // Still schedule next run
        });
      }, msUntilNextRun);
    };
    
    scheduleNextCalculation();
    console.log('🚀 Automatic daily average calculation started');
  }

  // Stop automatic calculation
  stopAutomaticCalculation() {
    if (this.intervalId) {
      clearTimeout(this.intervalId);
      this.intervalId = null;
    }
    this.isRunning = false;
    console.log('🛑 Automatic daily average calculation stopped');
  }

  // Manual calculation for specific date range
  async calculateDateRange(startDate, endDate, location = null) {
    try {
      const start = moment(startDate);
      const end = moment(endDate);
      const locations = location ? [location] : ['deans-office', 'lecture-hall', 'basement'];
      
      console.log(`📊 Calculating daily averages from ${startDate} to ${endDate}...`);
      
      const current = start.clone();
      while (current.isSameOrBefore(end)) {
        const dateStr = current.format('YYYY-MM-DD');
        
        for (const loc of locations) {
          await this.calculateLocationDailyAverage(loc, dateStr);
        }
        
        current.add(1, 'day');
      }
      
      console.log(`✅ Date range calculation completed`);
    } catch (error) {
      console.error('❌ Error calculating date range:', error);
      throw error;
    }
  }

  // Get calculation status
  getStatus() {
    return {
      isRunning: this.isRunning,
      nextCalculation: this.intervalId ? 'Scheduled' : 'Not scheduled'
    };
  }
}

// Export singleton instance
const dailyAverageService = new DailyAverageService();

module.exports = {
  DailyAverageService,
  dailyAverageService
};

