#!/usr/bin/env node

/**
 * Migrate Hall Data to Sensor_Data with Exact Timestamps
 * 
 * This script processes all existing hall data and stores it in the selected
 * Sensor_Data category using the exact same timestamps from hall data.
 * 
 * Usage: node migrate-hall-data.js [category] [date]
 * Examples:
 *   node migrate-hall-data.js Basement 2025-07-20
 *   node migrate-hall-data.js "Lecture Hall" today
 */

const { FirebaseService, getDatabase } = require('./firebase');

async function migrateHallData(targetCategory = null, targetDate = null) {
  console.log('🔄 Starting Hall Data Migration...\n');
  
  try {
    // Get active category if not specified
    if (!targetCategory) {
      const activeCategory = await FirebaseService.getActiveSensorDataCategory();
      if (activeCategory.category) {
        targetCategory = activeCategory.category;
        console.log(`📁 Using active Sensor_Data category: ${targetCategory}`);
      } else {
        console.log('❌ No target category specified and no active category set');
        console.log('Usage: node migrate-hall-data.js [category] [date]');
        console.log('Available categories: Basement, "Lecture Hall", "Dean\'s Office"');
        return;
      }
    }
    
    // Set target date
    if (!targetDate || targetDate === 'today') {
      targetDate = new Date().toISOString().split('T')[0];
    }
    
    console.log(`📅 Target date: ${targetDate}`);
    console.log(`📁 Target category: ${targetCategory}\n`);
    
    const db = getDatabase();
    
    // Get hall data
    console.log('📊 Fetching hall data...');
    const hallRef = db.ref('hall/data');
    const hallSnapshot = await hallRef.once('value');
    
    if (!hallSnapshot.exists()) {
      console.log('❌ No hall data found');
      return;
    }
    
    const hallData = hallSnapshot.val();
    console.log(`✅ Found hall data for ${Object.keys(hallData).length} dates`);
    
    // Check if target date exists
    if (!hallData[targetDate]) {
      console.log(`❌ No hall data found for date: ${targetDate}`);
      console.log(`📅 Available dates: ${Object.keys(hallData).join(', ')}`);
      return;
    }
    
    const dateData = hallData[targetDate];
    const timeEntries = Object.keys(dateData);
    console.log(`📊 Found ${timeEntries.length} time entries for ${targetDate}\n`);
    
    // Process each time entry
    let successCount = 0;
    let errorCount = 0;
    
    console.log('🔄 Processing time entries...');
    
    for (const timeKey of timeEntries) {
      const sensorData = dateData[timeKey];
      
      try {
        // Extract the exact timestamp from the data
        const originalTimestamp = sensorData.timestamp; // e.g., "2025-07-20 00:11:09"
        
        if (!originalTimestamp) {
          console.log(`⚠️ No timestamp found for ${timeKey}, skipping`);
          continue;
        }
        
        // Store in Sensor_Data with exact timestamp
        await FirebaseService.writeSensorDataToCategoryWithHierarchy(
          targetCategory,
          sensorData,
          originalTimestamp
        );
        
        successCount++;
        
        // Progress indicator
        if (successCount % 10 === 0) {
          console.log(`   ✅ Processed ${successCount}/${timeEntries.length} entries...`);
        }
        
      } catch (error) {
        console.error(`❌ Error processing ${timeKey}:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n🎉 Migration completed!');
    console.log(`✅ Successfully migrated: ${successCount} entries`);
    console.log(`❌ Errors: ${errorCount} entries`);
    console.log(`📁 Target: Sensor_Data/${targetCategory}/${targetDate}/`);
    
    // Verify the migration
    console.log('\n🔍 Verifying migration...');
    const verifyRef = db.ref(`Sensor_Data/${targetCategory}/${targetDate}`);
    const verifySnapshot = await verifyRef.once('value');
    
    if (verifySnapshot.exists()) {
      const migratedData = verifySnapshot.val();
      const migratedCount = Object.keys(migratedData).length;
      console.log(`✅ Verification: Found ${migratedCount} entries in Sensor_Data`);
      
      // Show sample structure
      const sampleTime = Object.keys(migratedData)[0];
      const sampleData = migratedData[sampleTime];
      console.log(`\n📊 Sample migrated entry (${sampleTime}):`);
      console.log(`   🌡️ Temperature: ${sampleData.temperature}°C`);
      console.log(`   💧 Humidity: ${sampleData.humidity}%`);
      console.log(`   🌬️ CO2: ${sampleData.co2} ppm`);
      console.log(`   ⏰ Timestamp: ${sampleData.timestamp}`);
      
    } else {
      console.log('❌ Verification failed: No data found in target location');
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
const targetCategory = args[0];
const targetDate = args[1];

// Handle script execution
if (require.main === module) {
  migrateHallData(targetCategory, targetDate);
}

module.exports = { migrateHallData };
