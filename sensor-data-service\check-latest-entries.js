#!/usr/bin/env node

/**
 * Check Latest Entries in Sensor_Data
 * 
 * This script checks the latest entries in Sensor_Data to verify
 * that they are using exact hall timestamps.
 */

const { getDatabase } = require('./firebase');

async function checkLatestEntries() {
  console.log('🔍 Checking Latest Entries in Sensor_Data...\n');
  
  try {
    const db = getDatabase();
    const targetDate = '2025-07-20';
    const category = 'Basement';
    
    // Get latest Sensor_Data entries
    console.log('📊 Fetching latest Sensor_Data entries...');
    const sensorDataRef = db.ref(`Sensor_Data/${category}/${targetDate}`);
    const sensorDataSnapshot = await sensorDataRef.once('value');
    
    if (!sensorDataSnapshot.exists()) {
      console.log('❌ No Sensor_Data found');
      return;
    }
    
    const sensorData = sensorDataSnapshot.val();
    const sensorTimestamps = Object.keys(sensorData).sort();
    
    console.log(`✅ Found ${sensorTimestamps.length} Sensor_Data entries`);
    
    // Show the latest 10 entries
    console.log('\n🕐 Latest 10 timestamps in Sensor_Data:');
    const latestTimestamps = sensorTimestamps.slice(-10);
    latestTimestamps.forEach((time, index) => {
      const data = sensorData[time];
      const isHallFormat = data.timestamp && data.timestamp.includes(' ') && !data.timestamp.includes('T');
      const formatIcon = isHallFormat ? '✅' : '❌';
      console.log(`   ${index + 1}. ${time} ${formatIcon} - timestamp: "${data.timestamp}"`);
    });
    
    // Check for clustering around specific times
    console.log('\n🔍 Checking for timestamp clustering...');
    const timeGroups = {};
    sensorTimestamps.forEach(time => {
      const hourMin = time.substring(0, 5); // Get HH:MM part
      if (!timeGroups[hourMin]) {
        timeGroups[hourMin] = [];
      }
      timeGroups[hourMin].push(time);
    });
    
    // Find groups with multiple entries (clustering)
    const clusteredTimes = Object.entries(timeGroups).filter(([_, times]) => times.length > 1);
    
    if (clusteredTimes.length > 0) {
      console.log(`⚠️ Found ${clusteredTimes.length} timestamp clusters:`);
      clusteredTimes.slice(0, 5).forEach(([hourMin, times]) => {
        console.log(`   ${hourMin}:XX - ${times.length} entries: ${times.join(', ')}`);
      });
      if (clusteredTimes.length > 5) {
        console.log(`   ... and ${clusteredTimes.length - 5} more clusters`);
      }
    } else {
      console.log('✅ No timestamp clustering found - perfect 5-minute intervals!');
    }
    
    // Check hall data pattern for comparison
    console.log('\n📊 Checking hall data pattern...');
    const hallRef = db.ref(`hall/data/${targetDate}`);
    const hallSnapshot = await hallRef.once('value');
    
    if (hallSnapshot.exists()) {
      const hallData = hallSnapshot.val();
      const hallTimestamps = Object.keys(hallData).sort();
      
      console.log(`📊 Hall data: ${hallTimestamps.length} entries`);
      console.log('🕐 Latest 5 hall timestamps:');
      hallTimestamps.slice(-5).forEach((time, index) => {
        console.log(`   ${index + 1}. ${time}`);
      });
      
      // Check if latest Sensor_Data entries match hall pattern
      const latestHallTimes = hallTimestamps.slice(-10);
      const matchingTimes = latestTimestamps.filter(time => latestHallTimes.includes(time));
      
      console.log(`\n🔍 Matching timestamps: ${matchingTimes.length}/${latestTimestamps.length}`);
      if (matchingTimes.length === latestTimestamps.length) {
        console.log('✅ Perfect match! All latest Sensor_Data entries use exact hall timestamps.');
      } else {
        console.log('⚠️ Some entries may not be using exact hall timestamps.');
      }
    }
    
    // Show data quality check
    console.log('\n📊 Data Quality Check:');
    const correctFormatCount = latestTimestamps.filter(time => {
      const data = sensorData[time];
      return data.timestamp && data.timestamp.includes(' ') && !data.timestamp.includes('T');
    }).length;
    
    console.log(`✅ Correct timestamp format: ${correctFormatCount}/${latestTimestamps.length}`);
    console.log(`📊 Success rate: ${Math.round((correctFormatCount / latestTimestamps.length) * 100)}%`);
    
    if (correctFormatCount === latestTimestamps.length) {
      console.log('🎉 All latest entries are using exact hall timestamps!');
    } else {
      console.log('⚠️ Some entries are still using system-generated timestamps.');
    }
    
  } catch (error) {
    console.error('❌ Check failed:', error);
  }
}

// Handle script execution
if (require.main === module) {
  checkLatestEntries();
}

module.exports = { checkLatestEntries };
