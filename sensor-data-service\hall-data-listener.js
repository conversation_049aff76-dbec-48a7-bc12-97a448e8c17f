/**
 * Hall Data Auto-Duplication Listener Service
 * 
 * This service monitors the 'hall' section in Firebase for new sensor data
 * and automatically duplicates it to the 'Sensor_Data' section based on
 * the currently selected admin location.
 * 
 * Features:
 * - Real-time monitoring of hall/data for new entries
 * - Automatic duplication to Sensor_Data with correct location mapping
 * - Preserves exact timestamps from hall data
 * - Uses hierarchical date/time structure as shown in the user's image
 */

const { FirebaseService, getDatabase } = require('./firebase');

class HallDataListener {
  constructor() {
    this.isListening = false;
    this.listeners = new Map(); // Track multiple listeners
    this.processedEntries = new Set(); // Prevent duplicate processing
  }

  /**
   * Start monitoring hall data for auto-duplication
   */
  async start() {
    try {
      if (this.isListening) {
        console.log('⚠️ Hall data listener is already running');
        return;
      }

      console.log('🔥 Starting Hall Data Auto-Duplication Listener...');
      
      const db = getDatabase();
      
      // Monitor hall/data for new date entries
      const hallDataRef = db.ref('hall/data');
      
      // Listen for new date folders
      const dateListener = hallDataRef.on('child_added', async (dateSnapshot) => {
        const date = dateSnapshot.key; // e.g., "2025-07-21"
        
        console.log(`📅 Monitoring hall data for date: ${date}`);
        
        // Set up listener for time entries within this date
        await this.setupTimeListener(date);
      });
      
      this.listeners.set('dateListener', { ref: hallDataRef, listener: dateListener });
      
      // Also monitor existing dates for new time entries
      const existingDatesSnapshot = await hallDataRef.once('value');
      if (existingDatesSnapshot.exists()) {
        const existingDates = Object.keys(existingDatesSnapshot.val());
        console.log(`📊 Setting up listeners for ${existingDates.length} existing dates`);
        
        for (const date of existingDates) {
          await this.setupTimeListener(date);
        }
      }
      
      this.isListening = true;
      console.log('✅ Hall data auto-duplication listener started successfully');
      console.log('🔄 New sensor data in hall will be automatically duplicated to Sensor_Data');
      
    } catch (error) {
      console.error('❌ Error starting hall data listener:', error);
      throw error;
    }
  }

  /**
   * Set up listener for time entries within a specific date
   */
  async setupTimeListener(date) {
    try {
      const db = getDatabase();
      const dateRef = db.ref(`hall/data/${date}`);
      
      // Listen for new time entries
      const timeListener = dateRef.on('child_added', async (timeSnapshot) => {
        const timeKey = timeSnapshot.key; // e.g., "12:07:29"
        const sensorData = timeSnapshot.val();
        
        const entryId = `${date}_${timeKey}`;
        
        // Prevent duplicate processing
        if (this.processedEntries.has(entryId)) {
          return;
        }
        
        this.processedEntries.add(entryId);
        
        console.log(`📊 New hall sensor data: ${date} ${timeKey}`);
        
        await this.duplicateToSensorData(date, timeKey, sensorData);
      });
      
      this.listeners.set(`timeListener_${date}`, { ref: dateRef, listener: timeListener });
      
    } catch (error) {
      console.error(`❌ Error setting up time listener for ${date}:`, error);
    }
  }

  /**
   * Duplicate hall data to Sensor_Data section
   */
  async duplicateToSensorData(date, timeKey, sensorData) {
    try {
      // Check if this data already exists in ANY Sensor_Data category
      const originalTimestamp = sensorData.timestamp || `${date} ${timeKey}`;
      const [parsedDate, parsedTime] = this.parseTimestamp(originalTimestamp);
      
      // Check all three categories for existing data
      const allCategories = ['Lecture Hall', 'Basement', "Dean's Office"];
      const db = getDatabase();
      
      for (const category of allCategories) {
        const existingPath = `Sensor_Data/${category}/${parsedDate}/${parsedTime}`;
        const existingData = await db.ref(existingPath).once('value');
        
        if (existingData.exists()) {
          console.log(`⚠️ Data already exists in ${category} for ${originalTimestamp}, skipping duplication`);
          return;
        }
      }
      
      // Get current active location
      const activeLocation = await FirebaseService.getActiveLocation();
      
      if (!activeLocation.location) {
        console.log(`⚠️ No active location set, skipping duplication for ${date} ${timeKey}`);
        return;
      }
      
      // Map admin location to Sensor_Data category
      const locationToCategoryMap = {
        'lecture-hall': 'Lecture Hall',
        'basement': 'Basement',
        'deans-office': "Dean's Office"
      };
      
      const targetCategory = locationToCategoryMap[activeLocation.location];
      
      if (!targetCategory) {
        console.log(`⚠️ Unknown location: ${activeLocation.location}, skipping duplication`);
        return;
      }
      
      console.log(`🔄 Duplicating to Sensor_Data/${targetCategory} with timestamp: ${originalTimestamp}`);
      
      // Duplicate to Sensor_Data with hierarchical structure
      await FirebaseService.writeSensorDataToCategoryWithHierarchy(
        targetCategory,
        sensorData,
        originalTimestamp
      );
      
      console.log(`✅ Hall data auto-duplicated: ${date} ${timeKey} → Sensor_Data/${targetCategory}`);
      console.log(`📍 Active location: ${activeLocation.location} (set by: ${activeLocation.setBy})`);
      
    } catch (error) {
      console.error(`❌ Error duplicating hall data for ${date} ${timeKey}:`, error.message);
    }
  }

  /**
   * Stop the hall data listener
   */
  async stop() {
    try {
      if (!this.isListening) {
        console.log('⚠️ Hall data listener is not running');
        return;
      }
      
      console.log('🛑 Stopping Hall Data Auto-Duplication Listener...');
      
      // Remove all listeners
      for (const [name, { ref, listener }] of this.listeners) {
        ref.off('child_added', listener);
        console.log(`🔇 Stopped listener: ${name}`);
      }
      
      this.listeners.clear();
      this.processedEntries.clear();
      this.isListening = false;
      
      console.log('✅ Hall data auto-duplication listener stopped');
      
    } catch (error) {
      console.error('❌ Error stopping hall data listener:', error);
    }
  }

  /**
   * Get listener status
   */
  getStatus() {
    return {
      isListening: this.isListening,
      activeListeners: this.listeners.size,
      processedEntries: this.processedEntries.size
    };
  }

  /**
   * Clear processed entries cache (for testing)
   */
  clearCache() {
    this.processedEntries.clear();
    console.log('🧹 Cleared processed entries cache');
  }

  // Helper method to parse timestamp
  parseTimestamp(timestamp) {
    // Parse "2025-07-21 12:07:29" format
    const [date, time] = timestamp.split(' ');
    return [date, time];
  }
}

// Export singleton instance
const hallDataListener = new HallDataListener();

module.exports = {
  HallDataListener,
  hallDataListener
};

