#!/usr/bin/env node

/**
 * Restart Sensor Data Service and Test Hall Auto-Duplication
 * 
 * This script helps restart the sensor data service and test the new
 * hall data auto-duplication functionality.
 */

const { spawn, exec } = require('child_process');
const path = require('path');

async function killExistingService() {
  return new Promise((resolve) => {
    console.log('🔍 Checking for existing sensor data service...');
    
    // Kill any existing node processes on port 3003
    exec('netstat -ano | findstr :3003', (error, stdout) => {
      if (stdout) {
        console.log('🛑 Found existing service, attempting to stop...');
        const lines = stdout.split('\n');
        const pids = [];
        
        lines.forEach(line => {
          const match = line.match(/\s+(\d+)$/);
          if (match) {
            pids.push(match[1]);
          }
        });
        
        if (pids.length > 0) {
          pids.forEach(pid => {
            try {
              exec(`taskkill /F /PID ${pid}`, (killError) => {
                if (!killError) {
                  console.log(`✅ Killed process ${pid}`);
                }
              });
            } catch (e) {
              // Ignore errors
            }
          });
        }
      }
      
      setTimeout(resolve, 2000); // Wait 2 seconds
    });
  });
}

async function startService() {
  return new Promise((resolve, reject) => {
    console.log('🚀 Starting sensor data service...');
    
    const serviceProcess = spawn('node', ['server.js'], {
      cwd: __dirname,
      stdio: 'pipe'
    });
    
    let serviceReady = false;
    
    serviceProcess.stdout.on('data', (data) => {
      const output = data.toString();
      console.log(output.trim());
      
      if (output.includes('Service ready for air quality monitoring')) {
        serviceReady = true;
        resolve(serviceProcess);
      }
    });
    
    serviceProcess.stderr.on('data', (data) => {
      console.error('Service Error:', data.toString());
    });
    
    serviceProcess.on('close', (code) => {
      if (!serviceReady) {
        reject(new Error(`Service exited with code ${code}`));
      }
    });
    
    // Timeout after 30 seconds
    setTimeout(() => {
      if (!serviceReady) {
        serviceProcess.kill();
        reject(new Error('Service startup timeout'));
      }
    }, 30000);
  });
}

async function runTest() {
  return new Promise((resolve, reject) => {
    console.log('\n🧪 Running hall auto-duplication test...');
    
    const testProcess = spawn('node', ['test-hall-auto-duplication.js', 'lecture-hall'], {
      cwd: __dirname,
      stdio: 'pipe'
    });
    
    testProcess.stdout.on('data', (data) => {
      console.log(data.toString().trim());
    });
    
    testProcess.stderr.on('data', (data) => {
      console.error('Test Error:', data.toString());
    });
    
    testProcess.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Test failed with code ${code}`));
      }
    });
  });
}

async function main() {
  try {
    console.log('🔄 Restarting Sensor Data Service with Hall Auto-Duplication...\n');
    
    // Step 1: Kill existing service
    await killExistingService();
    
    // Step 2: Start new service
    const serviceProcess = await startService();
    
    // Step 3: Wait a bit for service to fully initialize
    console.log('\n⏳ Waiting for service to fully initialize...');
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Step 4: Run test
    await runTest();
    
    console.log('\n✅ All tests completed successfully!');
    console.log('🔄 Hall data auto-duplication is now active');
    console.log('📊 New sensor data in "hall" will be automatically duplicated to "Sensor_Data"');
    console.log('📍 Based on the currently selected admin location');
    
    // Keep service running
    console.log('\n🏃 Service is running. Press Ctrl+C to stop.');
    
    process.on('SIGINT', () => {
      console.log('\n🛑 Stopping service...');
      serviceProcess.kill();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('\n❌ Failed to restart and test:', error.message);
    process.exit(1);
  }
}

// Run the main function
main();
