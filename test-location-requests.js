#!/usr/bin/env node

/**
 * Simple Location Service Request Test
 * 
 * This script tests if the location service is receiving and responding to requests
 * to help debug why "Location requested" messages might not be appearing.
 */

const http = require('http');

const LOCATION_SERVICE_URL = 'http://localhost:3002';

async function testLocationRequests() {
  console.log('🧪 Testing Location Service Requests...\n');

  try {
    // Test 1: Health check
    console.log('1. Testing health endpoint...');
    await makeRequest('/health', 'GET');

    // Test 2: Get current location
    console.log('\n2. Testing get location endpoint...');
    await makeRequest('/api/location', 'GET');

    // Test 3: Set location (admin)
    console.log('\n3. Testing set location endpoint...');
    await makeRequest('/api/location', 'POST', {
      location: 'basement',
      isAdmin: true,
      adminId: 'test-admin'
    });

    // Test 4: Get location again to see if it changed
    console.log('\n4. Testing get location after update...');
    await makeRequest('/api/location', 'GET');

    console.log('\n✅ All tests completed successfully!');
    console.log('📋 Check the location service terminal (SYNC) for the "Location requested" messages.');

  } catch (error) {
    console.error('\n❌ Test failed:', error.message);
    console.log('\n🔍 Troubleshooting:');
    console.log('   1. Make sure the location service is running on port 3002');
    console.log('   2. Check if the location service terminal shows any errors');
    console.log('   3. Verify the location service is started with: npm run dev');
  }
}

function makeRequest(path, method, data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          console.log(`   ✅ ${method} ${path} - Status: ${res.statusCode}`);
          console.log(`   📄 Response:`, parsedData);
          resolve(parsedData);
        } catch (error) {
          console.log(`   ✅ ${method} ${path} - Status: ${res.statusCode}`);
          console.log(`   📄 Response: ${responseData}`);
          resolve(responseData);
        }
      });
    });

    req.on('error', (error) => {
      console.log(`   ❌ ${method} ${path} - Error: ${error.message}`);
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Run the test
if (require.main === module) {
  testLocationRequests().catch(error => {
    console.error('❌ Test execution failed:', error);
    process.exit(1);
  });
}

module.exports = { testLocationRequests };
