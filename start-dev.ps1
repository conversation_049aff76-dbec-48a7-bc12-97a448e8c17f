# PowerShell script to start all AirSense services
Write-Host "🚀 Starting AirSense Development Environment" -ForegroundColor Green
Write-Host "=============================================" -ForegroundColor Green

# Kill any existing node processes
Write-Host "🧹 Cleaning up existing processes..." -ForegroundColor Yellow
Get-Process -Name node -ErrorAction SilentlyContinue | Stop-Process -Force
Start-Sleep -Seconds 2

# Function to start a service in a new window
function Start-Service {
    param(
        [string]$Name,
        [string]$Path,
        [string]$Command,
        [string]$Color
    )
    
    Write-Host "🔄 Starting $Name..." -ForegroundColor $Color
    
    $scriptBlock = {
        param($path, $command, $name)
        Set-Location $path
        Write-Host "Starting $name in $(Get-Location)" -ForegroundColor Green
        Invoke-Expression $command
    }
    
    Start-Job -Name $Name -ScriptBlock $scriptBlock -ArgumentList $Path, $Command, $Name
    Start-Sleep -Seconds 1
}

# Start Location Service
Start-Service -Name "LocationService" -Path "C:\Users\<USER>\OneDrive\Documents\GitHub\Air-Sense\location-service" -Command "node server.js" -Color "Green"

# Wait a bit for location service to start
Start-Sleep -Seconds 3

# Start Admin App
Start-Service -Name "AdminApp" -Path "C:\Users\<USER>\OneDrive\Documents\GitHub\Air-Sense\airsense-admin" -Command "npm start" -Color "Magenta"

# Wait a bit for admin app to start
Start-Sleep -Seconds 3

# Start Main App
Start-Service -Name "MainApp" -Path "C:\Users\<USER>\OneDrive\Documents\GitHub\Air-Sense\airsense-react" -Command "npm run dev:main" -Color "Cyan"

# Wait for services to start
Start-Sleep -Seconds 5

Write-Host ""
Write-Host "✅ Services Starting..." -ForegroundColor Green
Write-Host "📱 Main App: http://localhost:5173 (or next available port)" -ForegroundColor Cyan
Write-Host "👑 Admin App: http://localhost:3004" -ForegroundColor Magenta
Write-Host "🔌 Location Service: http://localhost:3002" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Job Status:" -ForegroundColor Yellow
Get-Job | Format-Table

Write-Host ""
Write-Host "🛠️  Commands:" -ForegroundColor Yellow
Write-Host "  Get-Job                    - Check service status"
Write-Host "  Receive-Job -Name [Name]   - View service output"
Write-Host "  Stop-Job -Name [Name]      - Stop a service"
Write-Host "  Remove-Job -Name [Name]    - Remove completed job"
Write-Host ""
Write-Host "🎯 Open your browser to the Main App URL shown above!" -ForegroundColor Green
