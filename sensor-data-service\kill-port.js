/**
 * Script to kill processes using port 3003
 * Run with: node kill-port.js
 */

const { exec } = require('child_process');
const PORT = 3003;

console.log(`🔍 Finding processes using port ${PORT}...`);

// For Windows
exec(`netstat -ano | findstr :${PORT}`, (error, stdout, stderr) => {
  if (error) {
    console.error(`❌ Error finding processes: ${error.message}`);
    return;
  }
  
  if (stderr) {
    console.error(`❌ Error: ${stderr}`);
    return;
  }
  
  const lines = stdout.trim().split('\n');
  if (lines.length === 0) {
    console.log(`✅ No processes found using port ${PORT}`);
    return;
  }
  
  console.log(`🔍 Found processes using port ${PORT}:`);
  console.log(stdout);
  
  // Extract PIDs
  const pids = new Set();
  lines.forEach(line => {
    const parts = line.trim().split(/\s+/);
    if (parts.length > 4) {
      pids.add(parts[4]);
    }
  });
  
  if (pids.size === 0) {
    console.log(`❌ Could not extract PIDs from netstat output`);
    return;
  }
  
  console.log(`🔍 Found PIDs: ${Array.from(pids).join(', ')}`);
  
  // Kill each process
  pids.forEach(pid => {
    console.log(`🔪 Killing process with PID ${pid}...`);
    exec(`taskkill /F /PID ${pid}`, (killError, killStdout, killStderr) => {
      if (killError) {
        console.error(`❌ Error killing process ${pid}: ${killError.message}`);
        return;
      }
      
      if (killStderr) {
        console.error(`❌ Error: ${killStderr}`);
        return;
      }
      
      console.log(`✅ Process ${pid} killed successfully`);
      console.log(killStdout);
    });
  });
});