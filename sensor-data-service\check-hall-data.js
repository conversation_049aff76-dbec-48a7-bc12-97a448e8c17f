#!/usr/bin/env node

/**
 * Check Hall Data Structure
 * 
 * This script examines the current structure of data in the "hall" location
 * to understand the timestamp format and data structure for exact replication
 * in Sensor_Data storage.
 */

const { getDatabase } = require('./firebase');

async function checkHallData() {
  console.log('🔍 Checking Hall Data Structure...\n');
  
  try {
    const db = getDatabase();
    
    // Get today's date for filtering
    const today = new Date().toISOString().split('T')[0]; // 2025-07-20
    console.log(`📅 Looking for today's data: ${today}\n`);
    
    // Check hall data structure
    console.log('📊 Checking hall data structure...');
    const hallRef = db.ref('hall');
    const hallSnapshot = await hallRef.once('value');
    
    if (!hallSnapshot.exists()) {
      console.log('❌ No hall data found');
      return;
    }
    
    const hallData = hallSnapshot.val();
    console.log('✅ Hall data structure:');
    console.log(Object.keys(hallData));
    
    // Check if there's a data subfolder
    if (hallData.data) {
      console.log('\n📊 Hall/data structure:');
      const dataKeys = Object.keys(hallData.data);
      console.log(`📊 Total timestamps: ${dataKeys.length}`);
      
      // Show recent timestamps
      const recentKeys = dataKeys.slice(-10);
      console.log('\n🕐 Recent timestamps:');
      recentKeys.forEach(key => {
        console.log(`   ${key}`);
      });
      
      // Check today's data specifically
      const todayKeys = dataKeys.filter(key => key.startsWith(today));
      console.log(`\n📅 Today's data (${today}): ${todayKeys.length} entries`);
      
      if (todayKeys.length > 0) {
        console.log('\n🕐 Today\'s timestamps:');
        todayKeys.slice(-10).forEach(key => {
          console.log(`   ${key}`);
        });
        
        // Show sample data structure
        const sampleKey = todayKeys[todayKeys.length - 1];
        const sampleData = hallData.data[sampleKey];
        console.log(`\n📊 Sample data structure (${sampleKey}):`);
        console.log(JSON.stringify(sampleData, null, 2));
        
        // Extract time from timestamp for hierarchy
        if (sampleKey.includes(' ')) {
          const [date, time] = sampleKey.split(' ');
          console.log(`\n🏗️ Hierarchical structure would be:`);
          console.log(`   Date: ${date}`);
          console.log(`   Time: ${time}`);
          console.log(`   Path: Sensor_Data/{category}/${date}/${time}/`);
        }
      }
    }
    
    // Check if there are direct timestamp entries
    const directTimestamps = Object.keys(hallData).filter(key => 
      key !== 'data' && key.includes('2025')
    );
    
    if (directTimestamps.length > 0) {
      console.log('\n📊 Direct timestamp entries:');
      directTimestamps.slice(-5).forEach(key => {
        console.log(`   ${key}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error checking hall data:', error);
  }
}

// Handle script execution
if (require.main === module) {
  checkHallData();
}

module.exports = { checkHallData };
