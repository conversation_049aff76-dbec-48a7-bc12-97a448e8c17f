# AirSense React App - Testing Guide

## ✅ **Conversion Verification Checklist**

### **1. Visual Design & UI/UX (100% Match Required)**

#### Home Page (`/`)
- [ ] **Hero Section**: Background image displays correctly
- [ ] **Logo**: AirSense logo shows with correct size and border radius
- [ ] **Location Selector**: Dropdown with home icon functionality
- [ ] **Features Carousel**: Bootstrap carousel with 4 slides
  - [ ] Real-Time Monitoring slide with live_air.jpg
  - [ ] Historical Data slide with history.jpg  
  - [ ] Air Quality Measurement slide with air_index.jpg
  - [ ] Comparison slide with comparison.png
- [ ] **Carousel Controls**: Previous/Next buttons and indicators work
- [ ] **Footer**: Social media icons (Facebook, Twitter, LinkedIn)

#### Dashboard Page (`/dashboard`)
- [ ] **Welcome Section**: Header with subtitle
- [ ] **Air Quality Scale**: Color gradient bar (Green → Yellow → Red)
- [ ] **Location Display**: Shows selected location from localStorage
- [ ] **Metrics Grid**: 9 air quality metrics + AQI display
  - [ ] Temperature (°C)
  - [ ] Humidity (%)
  - [ ] PM2.5 (µg/m³)
  - [ ] PM10 (µg/m³)
  - [ ] Nitrogen Dioxide (ppb)
  - [ ] Ozone (ppb)
  - [ ] CO2 Level (ppm)
  - [ ] Pressure (hPa)
  - [ ] Gas Resistance (Ω)
  - [ ] AQI (calculated)
- [ ] **Status Colors**: Good (Green), Moderate (Yellow), Poor (Red)
- [ ] **Real-time Updates**: Data refreshes every 30 seconds
- [ ] **Refresh Button**: Manual data refresh works
- [ ] **Historical Data Section**: Chart placeholder with controls

#### Comparison Page (`/comparison`)
- [ ] **Location Selection**: Two side-by-side location cards
- [ ] **Date Pickers**: Date selection for both locations
- [ ] **Metric Dropdowns**: All 9 metrics available
- [ ] **Gauge Displays**: Large value display with units
- [ ] **Status Indicators**: Color-coded quality status
- [ ] **Validation**: Error message for same location selection
- [ ] **Comparison Summary**: Dynamic text based on selections
- [ ] **Legend**: Good/Moderate/Poor color indicators
- [ ] **Daily Distribution**: Chart placeholder

#### Navigation & Layout
- [ ] **Navbar**: Logo, navigation links, location selector, home icon
- [ ] **Active States**: Current page highlighted in navigation
- [ ] **Responsive Design**: Works on mobile and desktop
- [ ] **Footer**: Copyright, links, social media icons

### **2. Functionality Testing**

#### Location Management
- [ ] **localStorage Persistence**: Location selection saved and restored
- [ ] **Home Icon**: Clears location and resets to home
- [ ] **Location Display**: Shows in navbar and dashboard
- [ ] **Validation**: Prevents same location comparison

#### Data Generation & Display
- [ ] **Mock Data**: Realistic air quality values generated
- [ ] **Quality Assessment**: Correct status calculation based on thresholds
- [ ] **Units Display**: Proper units shown for each metric
- [ ] **AQI Calculation**: Air Quality Index computed from PM2.5
- [ ] **Real-time Updates**: Automatic data refresh

#### User Interactions
- [ ] **Carousel Navigation**: Manual slide control works
- [ ] **Form Validation**: Comparison page validates inputs
- [ ] **Date Selection**: Date pickers function correctly
- [ ] **Dropdown Selections**: All dropdowns work properly
- [ ] **Button Actions**: All buttons perform expected actions

### **3. Technical Implementation**

#### React Architecture
- [ ] **Component Structure**: Proper separation of concerns
- [ ] **State Management**: React hooks used correctly
- [ ] **Props Passing**: Data flows correctly between components
- [ ] **Effect Hooks**: Side effects handled properly
- [ ] **Event Handling**: User interactions work smoothly

#### Styling & Assets
- [ ] **CSS Variables**: Design system colors used consistently
- [ ] **Bootstrap Integration**: Grid system and components work
- [ ] **Image Loading**: All images load from public folder
- [ ] **Responsive Classes**: Mobile-friendly layout
- [ ] **Custom Styles**: Original design preserved exactly

#### Performance & UX
- [ ] **Fast Loading**: Pages load quickly
- [ ] **Smooth Transitions**: Hover effects and animations work
- [ ] **Error Handling**: Graceful fallbacks for missing data
- [ ] **Accessibility**: Basic accessibility features present

### **4. Original Feature Preservation**

#### From home1.html & home2.html
- [ ] **Exact Layout**: Hero section matches original
- [ ] **Same Content**: All text and images preserved
- [ ] **Carousel Functionality**: Bootstrap carousel works identically
- [ ] **Location Selector**: Same dropdown options and behavior

#### From dashboard.html & dashboard.js
- [ ] **Air Quality Standards**: Same thresholds and calculations
- [ ] **Metrics Display**: All 9 metrics with correct units
- [ ] **Status Colors**: Same color scheme (green/yellow/red)
- [ ] **Real-time Simulation**: Mock data generation matches original

#### From comparison.html & comparison.js
- [ ] **Location Options**: Same 4 locations available
- [ ] **Metric Selection**: All 9 metrics selectable
- [ ] **Quality Assessment**: Same calculation logic
- [ ] **Validation Logic**: Same error handling

### **5. Browser Compatibility**

- [ ] **Chrome**: Latest version works perfectly
- [ ] **Firefox**: Latest version works perfectly
- [ ] **Safari**: Latest version works perfectly
- [ ] **Edge**: Latest version works perfectly
- [ ] **Mobile Browsers**: Responsive design works on mobile

### **6. Development Experience**

- [ ] **Hot Reload**: Changes reflect immediately during development
- [ ] **Error Handling**: Clear error messages in console
- [ ] **TypeScript**: Type safety throughout the application
- [ ] **Build Process**: Production build works correctly

## **Testing Instructions**

### **Quick Test (5 minutes)**
1. Open http://localhost:5173
2. Navigate through all 3 pages (Home, Dashboard, Comparison)
3. Select a location in the navbar
4. Verify location persists across page navigation
5. Test carousel on home page
6. Check metrics display on dashboard
7. Try comparison between two locations

### **Comprehensive Test (15 minutes)**
1. **Home Page**:
   - Test carousel navigation (manual and automatic)
   - Verify all images load correctly
   - Test location selector and home icon
   - Check footer social media links

2. **Dashboard Page**:
   - Verify all 10 metrics display with correct units
   - Check color coding (good/moderate/poor)
   - Test refresh button
   - Verify real-time updates (wait 30 seconds)
   - Test historical data controls

3. **Comparison Page**:
   - Select two different locations
   - Try different metrics for each location
   - Test date selection
   - Verify validation for same location
   - Check comparison summary text

4. **Navigation & Persistence**:
   - Test location selection persistence
   - Verify active navigation states
   - Test home icon functionality
   - Check responsive design on mobile

## **Success Criteria**

✅ **PASS**: All checkboxes above are checked
❌ **FAIL**: Any checkbox fails - requires immediate fix

The React application should be a **pixel-perfect, functionally-identical** conversion of the original HTML/CSS/JS files with modern React architecture and improved maintainability.
