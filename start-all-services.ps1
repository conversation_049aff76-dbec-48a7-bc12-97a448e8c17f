# AirSense - Complete System Startup Script
# This script starts all services including Firebase sensor data service

Write-Host "🚀 Starting AirSense Complete System..." -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Cyan

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js is not installed. Please install Node.js first." -ForegroundColor Red
    exit 1
}

# Function to check if a port is in use
function Test-Port {
    param([int]$Port)
    try {
        $connection = New-Object System.Net.Sockets.TcpClient
        $connection.Connect("localhost", $Port)
        $connection.Close()
        return $true
    } catch {
        return $false
    }
}

# Check required ports
$ports = @{
    3001 = "Sensor Data Service (Firebase)"
    3002 = "Location Service"
    3004 = "Admin App"
    3005 = "Contact Service"
    5173 = "Main App (Vite)"
}

Write-Host "`n🔍 Checking port availability..." -ForegroundColor Yellow
foreach ($port in $ports.Keys) {
    if (Test-Port -Port $port) {
        Write-Host "⚠️  Port $port is already in use (${ports[$port]})" -ForegroundColor Yellow
    } else {
        Write-Host "✅ Port $port is available (${ports[$port]})" -ForegroundColor Green
    }
}

# Start services in separate PowerShell windows
Write-Host "`n🚀 Starting services..." -ForegroundColor Green

# Start Sensor Data Service (Firebase) - Port 3001
Write-Host "🔥 Starting Sensor Data Service - Firebase (Port 3001)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'sensor-data-service'; Write-Host '🔥 Firebase Sensor Data Service Starting...' -ForegroundColor Green; npm run dev"

# Wait a moment
Start-Sleep -Seconds 2

# Start Location Service - Port 3002
Write-Host "📍 Starting Location Service (Port 3002)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'location-service'; Write-Host '🚀 Location Service Starting...' -ForegroundColor Green; npm run dev"

# Wait a moment
Start-Sleep -Seconds 2

# Start Admin App - Port 3004
Write-Host "👑 Starting Admin App (Port 3004)..." -ForegroundColor Cyan
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'airsense-admin'; Write-Host '👑 Admin App Starting...' -ForegroundColor Green; npm start"

# Wait a moment
Start-Sleep -Seconds 2

# Start Contact Service - Port 3005
Write-Host "📧 Starting Contact Service (Port 3005)..." -ForegroundColor Magenta
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd 'contact-service'; Write-Host '📧 Contact Service Starting...' -ForegroundColor Magenta; npm run dev"

# Wait a moment
Start-Sleep -Seconds 3

# Start Main App (this will be the primary window)
Write-Host "🎯 Starting Main App (Port 5173)..." -ForegroundColor Cyan
Write-Host "`n" -ForegroundColor White
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host "🎉 AirSense System Started!" -ForegroundColor Green
Write-Host "=====================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "📱 Services Running:" -ForegroundColor Yellow
Write-Host "  • Main App:           http://localhost:5173/" -ForegroundColor White
Write-Host "  • Admin App:          http://localhost:3004/" -ForegroundColor White
Write-Host "  • Location Service:   http://localhost:3002/" -ForegroundColor White
Write-Host "  • Sensor Data API:    http://localhost:3001/" -ForegroundColor White
Write-Host "  • Contact Service:    http://localhost:3005/" -ForegroundColor Magenta
Write-Host ""
Write-Host "🔥 Firebase Integration:" -ForegroundColor Yellow
Write-Host "  • Realtime Database:  Connected" -ForegroundColor White
Write-Host "  • WebSocket Updates:  Active" -ForegroundColor White
Write-Host "  • Daily Averages:     Automated" -ForegroundColor White
Write-Host ""
Write-Host "📊 API Endpoints:" -ForegroundColor Yellow
Write-Host "  • Health Check:       GET /health" -ForegroundColor White
Write-Host "  • Latest Data:        GET /api/sensor-data/:location/latest" -ForegroundColor White
Write-Host "  • Historical Data:    GET /api/sensor-data/:location/history" -ForegroundColor White
Write-Host "  • ESP32 Data Input:   POST /api/sensor-data/:location" -ForegroundColor White
Write-Host ""
Write-Host "🧪 Testing:" -ForegroundColor Yellow
Write-Host "  • Run tests:          cd sensor-data-service && npm test" -ForegroundColor White
Write-Host "  • ESP32 Simulation:   Included in test suite" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  To stop all services, close all PowerShell windows" -ForegroundColor Yellow
Write-Host "=====================================" -ForegroundColor Cyan

# Change to main app directory and start
cd airsense-react
Write-Host "🎯 Main App Starting..." -ForegroundColor Green
npm run dev
