# AirSense Admin Application

This is the admin interface for the AirSense air quality monitoring system. It provides secure authentication and location management capabilities with multi-machine synchronization.

## 🎯 Purpose

The admin app handles:
- **Admin Authentication**: Secure login for administrators
- **Location Management**: Select and manage monitoring locations
- **Multi-Machine Sync**: Location changes propagate to all connected machines
- **Session Management**: Persistent admin sessions across the system

## 🚀 Quick Start

### Standalone Usage
```bash
npm start
```
Runs the admin app on [http://localhost:3004](http://localhost:3004)

### Integrated Usage (Recommended)
```bash
# From the main airsense-react directory
npm run dev
```
This starts the admin app along with the main app and location sync service.

## 🔐 Admin Credentials

- **Email**: `<EMAIL>`
- **Password**: `admin123`

## 📍 Available Locations

- **Dean's Office** - Administrative area monitoring
- **Lecture Hall** - Classroom environment tracking
- **Basement** - Lower level air quality monitoring

## 🌐 Multi-Machine Synchronization

When an admin selects a location:
1. **Location is stored** in the central location service
2. **All connected machines** receive real-time updates via WebSocket
3. **Location persists** across browser sessions and machine restarts
4. **Visual indicators** show admin status with crown icons

## 🔄 Workflow

1. **Access**: Click "Admin Login" in the main app footer
2. **Authenticate**: Enter admin credentials
3. **Select Location**: Choose from available monitoring locations
4. **Sync**: Location appears on all machines running the website
5. **Manage**: Use home icon to return to location selection

## 🛠️ Available Scripts

### `npm start`
Runs the app in development mode on port 3004.

### `npm run build`
Builds the app for production to the `build` folder.

### `npm run eject`

**Note: this is a one-way operation. Once you `eject`, you can’t go back!**

If you aren’t satisfied with the build tool and configuration choices, you can `eject` at any time. This command will remove the single build dependency from your project.

Instead, it will copy all the configuration files and the transitive dependencies (webpack, Babel, ESLint, etc) right into your project so you have full control over them. All of the commands except `eject` will still work, but they will point to the copied scripts so you can tweak them. At this point you’re on your own.

You don’t have to ever use `eject`. The curated feature set is suitable for small and middle deployments, and you shouldn’t feel obligated to use this feature. However we understand that this tool wouldn’t be useful if you couldn’t customize it when you are ready for it.

## Learn More

You can learn more in the [Create React App documentation](https://facebook.github.io/create-react-app/docs/getting-started).

To learn React, check out the [React documentation](https://reactjs.org/).

## 🔧 Technical Details

- **Framework**: Create React App with TypeScript
- **Port**: 3004 (configurable via PORT environment variable)
- **Authentication**: Simple credential-based login
- **Storage**: localStorage for session persistence
- **Communication**: REST API calls to location service
- **Styling**: CSS3 with responsive design

## 🔗 Integration

This admin app is designed to work seamlessly with:
- **Main App** (airsense-react) - Primary user interface
- **Location Service** (location-service) - Multi-machine synchronization
- **WebSocket Events** - Real-time location updates

## 📝 Development Notes

- Built with Create React App for rapid development
- TypeScript for type safety
- Responsive design for mobile and desktop
- Graceful fallback if location service is unavailable
- Session persistence across page reloads
