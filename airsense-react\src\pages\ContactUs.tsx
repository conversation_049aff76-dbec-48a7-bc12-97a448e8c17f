import React, { useState } from 'react';
import Layout from '../layouts/Layout';

const ContactUs: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    message: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{
    type: 'success' | 'error' | null;
    message: string;
    details?: string[];
  }>({
    type: null,
    message: '',
    details: []
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Clear previous status
    setSubmitStatus({ type: null, message: '', details: [] });
    setIsSubmitting(true);

    try {
      const response = await fetch('http://localhost:3005/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        setSubmitStatus({
          type: 'success',
          message: 'Thank you for your message! We will get back to you soon.',
          details: []
        });

        // Reset form on success
        setFormData({
          name: '',
          email: '',
          subject: '',
          message: ''
        });
      } else {
        setSubmitStatus({
          type: 'error',
          message: result.error || 'Failed to submit contact form',
          details: result.details || []
        });
      }
    } catch (error) {
      console.error('Contact form submission error:', error);
      setSubmitStatus({
        type: 'error',
        message: 'Unable to submit contact form. Please check your connection and try again.',
        details: []
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Layout showNavbar={true}>
      <div className="contact-us-container">
        <main className="contact-main-container">
          <div className="contact-sections-wrapper">
            <div className="contact-left-section">
                  <section className="mb-5">
                    <h2>Get In Touch</h2>
                    <p>
                      Have questions about AirSense or need support? We'd love to hear from you. 
                      Send us a message and we'll respond as soon as possible.
                    </p>
                    
                    <div className="contact-info mt-4">
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-envelope"></i> Email</h5>
                        <p><EMAIL></p>
                      </div>
                      
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-telephone"></i> Phone</h5>
                        <p>+94 75 1654425</p>
                      </div>
                      
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-geo-alt"></i> Address</h5>
                        <p>
                          No 315<br />
                          Sirinanda Jothikarama road<br />
                          Thalawathugoda
                        </p>
                      </div>
                      
                      <div className="contact-item mb-3">
                        <h5><i className="bi bi-clock"></i> Business Hours</h5>
                        <p>
                          Monday - Friday: 9:00 AM - 6:00 PM<br />
                          Saturday: 10:00 AM - 4:00 PM<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </section>
            </div>

            <div className="contact-right-section">
                  <section className="mb-5">
                    <h2>Send us a Message</h2>

                    {/* Status Messages */}
                    {submitStatus.type && (
                      <div className={`alert ${submitStatus.type === 'success' ? 'alert-success' : 'alert-danger'} mb-3`}>
                        <div className="d-flex align-items-start">
                          <i className={`bi ${submitStatus.type === 'success' ? 'bi-check-circle' : 'bi-exclamation-triangle'} me-2`}></i>
                          <div>
                            <div>{submitStatus.message}</div>
                            {submitStatus.details && submitStatus.details.length > 0 && (
                              <ul className="mb-0 mt-2">
                                {submitStatus.details.map((detail, index) => (
                                  <li key={index}>{detail}</li>
                                ))}
                              </ul>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    <form onSubmit={handleSubmit} className="contact-form">
                      <div className="mb-3">
                        <label htmlFor="name" className="form-label">Name *</label>
                        <input
                          type="text"
                          className="form-control"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      
                      <div className="mb-3">
                        <label htmlFor="email" className="form-label">Email *</label>
                        <input
                          type="email"
                          className="form-control"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      
                      <div className="mb-3">
                        <label htmlFor="subject" className="form-label">Subject *</label>
                        <input
                          type="text"
                          className="form-control"
                          id="subject"
                          name="subject"
                          value={formData.subject}
                          onChange={handleInputChange}
                          required
                        />
                      </div>
                      
                      <div className="mb-3">
                        <label htmlFor="message" className="form-label">Message *</label>
                        <textarea
                          className="form-control"
                          id="message"
                          name="message"
                          rows={5}
                          value={formData.message}
                          onChange={handleInputChange}
                          required
                        ></textarea>
                      </div>
                      
                      <button
                        type="submit"
                        className="btn btn-primary"
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <>
                            <span className="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>
                            Sending...
                          </>
                        ) : (
                          'Send Message'
                        )}
                      </button>
                    </form>
                  </section>
                </div>
              </div>
        </main>
      </div>
    </Layout>
  );
};

export default ContactUs;
