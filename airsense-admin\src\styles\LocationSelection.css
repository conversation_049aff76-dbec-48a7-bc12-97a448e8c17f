/* Location Selection Page Styles */
body.location-selection-page {
  background-image: url('../background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  margin: 0;
  padding: 0;
  font-family: 'Segoe UI', system-ui, -apple-system, sans-serif;
  min-height: 100vh;
}

.location-selection-container {
  width: 100%;
  min-height: 100vh;
}

.hero {
  text-align: center;
  padding: 50px 20px;
  color: #011c40;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  position: relative;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(167, 235, 242, 0.3), rgba(84, 172, 191, 0.2));
  z-index: 1;
}

.hero > * {
  position: relative;
  z-index: 2;
}

.search-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  flex-wrap: wrap;
  max-width: 600px;
  padding: 0 1rem;
}

.home-icon-btn {
  background: none;
  border: none;
  outline: none;
  cursor: pointer;
  padding: 0.4rem;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

.home-icon-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.home-icon {
  height: 32px;
  width: 32px;
  border: none;
  outline: none;
}

.location-dropdown {
  padding: 0.75rem 1rem;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  font-size: 1rem;
  cursor: pointer;
  background: rgba(255, 255, 255, 0.9);
  color: #011c40;
  min-width: 200px;
  transition: all 0.3s ease;
}

.location-dropdown:focus {
  outline: none;
  border-color: #54ACBF;
  box-shadow: 0 0 0 3px rgba(84, 172, 191, 0.2);
}

.continue-button {
  background: linear-gradient(135deg, #54ACBF 0%, #26658C 100%);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.continue-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(84, 172, 191, 0.3);
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  color: #011c40;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(255, 255, 255, 0.5);
}

.logo {
  height: 300px;
  border-radius: 20px;
  margin: 2rem 0;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.hero p {
  font-size: 1.3rem;
  margin-bottom: 2rem;
  color: #26658C;
  text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.5);
}

.admin-info {
  background: rgba(84, 172, 191, 0.1);
  padding: 1rem 2rem;
  border-radius: 12px;
  border: 2px solid rgba(84, 172, 191, 0.3);
  margin-top: 2rem;
}

.admin-info p {
  color: #011c40;
  font-weight: 600;
  margin: 0;
  font-size: 1.1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .search-container {
    flex-direction: column;
    gap: 1rem;
  }
  
  .hero h1 {
    font-size: 2rem;
  }
  
  .hero p {
    font-size: 1.1rem;
  }
  
  .logo {
    height: 200px;
  }
  
  .location-dropdown {
    min-width: 250px;
  }
}
