{"name": "airsense-sensor-data-service", "version": "1.0.0", "description": "Firebase-powered sensor data service for AirSense air quality monitoring", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "node test-endpoints.js", "kill-port": "node kill-port.js"}, "keywords": ["airsense", "sensor-data", "firebase", "air-quality", "esp32", "realtime-database"], "author": "AirSense Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "firebase-admin": "^12.0.0", "helmet": "^7.1.0", "moment": "^2.29.4", "tcp-port-used": "^1.0.2", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}