const { FirebaseService } = require('./firebase');


class DelayedStorageService {
  constructor(delayMinutes = 0) {
    this.delayMinutes = delayMinutes;
    this.delayMs = delayMinutes * 60 * 1000; // Convert to milliseconds
    this.pendingData = new Map(); // Store pending data with timestamps
    this.storageTimer = null;
    this.isRunning = false;
    this.processingInterval = 30000; // Check every 30 seconds for data to store
  }

  /**
   * Start the delayed storage service
   */
  start() {
    if (this.isRunning) {
      console.log('⚠️ Delayed storage service is already running');
      return;
    }

    this.isRunning = true;
    console.log(`⏰ Starting delayed storage service with ${this.delayMinutes}-minute delay`);
    
    // Start the processing timer
    this.storageTimer = setInterval(() => {
      this.processDelayedData();
    }, this.processingInterval);

    console.log('✅ Delayed storage service started');
  }

  /**
   * Stop the delayed storage service
   */
  stop() {
    if (!this.isRunning) {
      return;
    }

    this.isRunning = false;
    
    if (this.storageTimer) {
      clearInterval(this.storageTimer);
      this.storageTimer = null;
    }

    console.log('🛑 Delayed storage service stopped');
  }

  /**
   * Add sensor data to the delayed storage queue
   */
  async queueSensorData(sensorData, location, deviceId = 'esp32-main') {
    try {
      const receivedAt = new Date();
      const storeAt = new Date(receivedAt.getTime() + this.delayMs);
      
      // Create unique ID for this data entry
      const dataId = `${deviceId}_${receivedAt.getTime()}`;
      
      // Enhanced sensor data with metadata
      const queuedData = {
        id: dataId,
        sensorData: sensorData,
        location: location,
        deviceId: deviceId,
        receivedAt: receivedAt.toISOString(),
        storeAt: storeAt.toISOString(),
        status: 'queued'
      };

      // Add to pending data map
      this.pendingData.set(dataId, queuedData);
      
      console.log(`⏰ Sensor data queued for delayed storage:`);
      console.log(`   📍 Location: ${location}`);
      console.log(`   📅 Received: ${receivedAt.toISOString()}`);
      console.log(`   ⏳ Will store at: ${storeAt.toISOString()}`);
      console.log(`   📊 Queue size: ${this.pendingData.size}`);

      return {
        success: true,
        dataId: dataId,
        receivedAt: receivedAt.toISOString(),
        willStoreAt: storeAt.toISOString(),
        delayMinutes: this.delayMinutes,
        queueSize: this.pendingData.size
      };

    } catch (error) {
      console.error('❌ Error queuing sensor data:', error);
      throw error;
    }
  }

  /**
   * Process delayed data that is ready to be stored
   */
  async processDelayedData() {
    if (this.pendingData.size === 0) {
      return; // No data to process
    }

    const now = new Date();
    const readyToStore = [];

    // Find data that is ready to be stored
    for (const [dataId, queuedData] of this.pendingData.entries()) {
      const storeAt = new Date(queuedData.storeAt);
      
      if (now >= storeAt) {
        readyToStore.push({ dataId, queuedData });
      }
    }

    if (readyToStore.length === 0) {
      return; // No data ready yet
    }

    console.log(`📦 Processing ${readyToStore.length} delayed sensor data entries...`);

    // Process each ready data entry
    for (const { dataId, queuedData } of readyToStore) {
      try {
        await this.storeSensorData(queuedData);
        
        // Remove from pending data
        this.pendingData.delete(dataId);
        
        console.log(`✅ Stored delayed sensor data: ${dataId}`);
        
      } catch (error) {
        console.error(`❌ Error storing delayed data ${dataId}:`, error);
        
        // Mark as failed but keep in queue for retry
        queuedData.status = 'failed';
        queuedData.lastError = error.message;
        queuedData.retryCount = (queuedData.retryCount || 0) + 1;
        
        // Remove after 3 failed attempts
        if (queuedData.retryCount >= 3) {
          console.error(`❌ Removing failed data after 3 attempts: ${dataId}`);
          this.pendingData.delete(dataId);
        }
      }
    }

    console.log(`📊 Delayed storage queue size: ${this.pendingData.size}`);
  }

  /**
   * Store sensor data in Firebase
   */
  async storeSensorData(queuedData) {
    const { sensorData, location, deviceId, receivedAt } = queuedData;
    
    // Calculate AQI
    const aqi = this.calculateAQI(sensorData);
    
    // Create timestamp for Firebase storage (use original received time)
    const timestamp = receivedAt;
    const firebaseTimestamp = timestamp.replace(/[.:#$[\]]/g, '_');

     // Remove any undefined values from sensorData
     const cleanSensorData = Object.fromEntries(
      Object.entries(sensorData).filter(([_, v]) => v !== undefined)
    );
    
    // Enhanced sensor data with metadata
const enhancedData = {
  ...cleanSensorData,
  aqi: aqi,
  deviceId: deviceId,
  timestamp: timestamp,
  location: location,
  receivedAt: receivedAt,
  storedAt: new Date().toISOString(),
  delayedStorage: true,
  delayMinutes: this.delayMinutes,
  createdAt: require('firebase-admin').database.ServerValue.TIMESTAMP
};


    // Store in Firebase using the enhanced Firebase service
    await FirebaseService.writeSensorData(location, firebaseTimestamp, enhancedData);

    console.log(`📝 Delayed sensor data stored for ${location} (originally received at ${receivedAt})`);

    // Also store in active Sensor_Data category if one is selected
    try {
      const activeCategory = await FirebaseService.getActiveSensorDataCategory();
      if (activeCategory.category) {
        // Use original timestamp from hall data if available
        const originalTimestamp = enhancedData.originalTimestamp || enhancedData.timestamp;
        await FirebaseService.writeSensorDataToCategoryWithHierarchy(
          activeCategory.category,
          enhancedData,
          originalTimestamp
        );
        console.log(`📁 Data also stored in Sensor_Data/${activeCategory.category} with exact timestamp: ${originalTimestamp}`);
      }
    } catch (error) {
      console.error('⚠️ Failed to store in Sensor_Data category (continuing with main storage):', error.message);
    }
  }

  /**
   * Calculate Air Quality Index (AQI) from sensor data
   */
  calculateAQI(sensorData) {
    try {
      // Simple AQI calculation based on PM2.5
      const pm25 = sensorData.pm25 || 0;
      
      let aqi;
      if (pm25 <= 12) {
        aqi = Math.round((50 / 12) * pm25);
      } else if (pm25 <= 35.4) {
        aqi = Math.round(((100 - 51) / (35.4 - 12.1)) * (pm25 - 12.1) + 51);
      } else if (pm25 <= 55.4) {
        aqi = Math.round(((150 - 101) / (55.4 - 35.5)) * (pm25 - 35.5) + 101);
      } else if (pm25 <= 150.4) {
        aqi = Math.round(((200 - 151) / (150.4 - 55.5)) * (pm25 - 55.5) + 151);
      } else if (pm25 <= 250.4) {
        aqi = Math.round(((300 - 201) / (250.4 - 150.5)) * (pm25 - 150.5) + 201);
      } else {
        aqi = Math.round(((500 - 301) / (500.4 - 250.5)) * (pm25 - 250.5) + 301);
      }
      
      return Math.max(0, Math.min(500, aqi));
    } catch (error) {
      console.error('❌ Error calculating AQI:', error);
      return 0;
    }
  }

  /**
   * Get current queue status
   */
  getQueueStatus() {
    const now = new Date();
    let readyCount = 0;
    let pendingCount = 0;
    let failedCount = 0;

    for (const queuedData of this.pendingData.values()) {
      const storeAt = new Date(queuedData.storeAt);
      
      if (queuedData.status === 'failed') {
        failedCount++;
      } else if (now >= storeAt) {
        readyCount++;
      } else {
        pendingCount++;
      }
    }

    return {
      total: this.pendingData.size,
      ready: readyCount,
      pending: pendingCount,
      failed: failedCount,
      delayMinutes: this.delayMinutes,
      isRunning: this.isRunning
    };
  }

  /**
   * Clear all pending data (use with caution)
   */
  clearQueue() {
    const clearedCount = this.pendingData.size;
    this.pendingData.clear();
    console.log(`🗑️ Cleared ${clearedCount} items from delayed storage queue`);
    return clearedCount;
  }

  /**
   * Get pending data for debugging
   */
  getPendingData() {
    return Array.from(this.pendingData.values());
  }
}

module.exports = DelayedStorageService;
