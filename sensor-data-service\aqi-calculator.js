/**
 * Air Quality Index (AQI) Calculator
 * Based on PM2.5 values using EPA standards
 */

// Calculate AQI based on PM2.5 values
function calculateAQI(sensorData) {
  const breakpoints = {
    pm25: [
      { low: 0, high: 12, aqiLow: 0, aqiHigh: 50 },
      { low: 12.1, high: 35.4, aqiLow: 51, aqiHigh: 100 },
      { low: 35.5, high: 55.4, aqiLow: 101, aqiHigh: 150 },
      { low: 55.5, high: 150.4, aqiLow: 151, aqiHigh: 200 },
      { low: 150.5, high: 250.4, aqiLow: 201, aqiHigh: 300 },
      { low: 250.5, high: 500.4, aqiLow: 301, aqiHigh: 500 }
    ]
  };

  // Get PM2.5 value from sensor data
  const pm25Value = sensorData.pm25 || 0;
  let aqi = 0;

  // Find the appropriate breakpoint and calculate AQI
  for (const bp of breakpoints.pm25) {
    if (pm25Value >= bp.low && pm25Value <= bp.high) {
      aqi = Math.round(((bp.aqiHigh - bp.aqiLow) / (bp.high - bp.low)) * (pm25Value - bp.low) + bp.aqiLow);
      break;
    }
  }

  // Ensure AQI is within valid range (0-500)
  return Math.min(500, Math.max(0, aqi));
}

// Get AQI category and color based on AQI value
function getAQICategory(aqi) {
  if (aqi <= 50) {
    return { category: 'Good', color: '#00E400', level: 'good' };
  } else if (aqi <= 100) {
    return { category: 'Moderate', color: '#FFFF00', level: 'moderate' };
  } else if (aqi <= 150) {
    return { category: 'Unhealthy for Sensitive Groups', color: '#FF7E00', level: 'poor' };
  } else if (aqi <= 200) {
    return { category: 'Unhealthy', color: '#FF0000', level: 'poor' };
  } else if (aqi <= 300) {
    return { category: 'Very Unhealthy', color: '#8F3F97', level: 'poor' };
  } else {
    return { category: 'Hazardous', color: '#7E0023', level: 'poor' };
  }
}

module.exports = {
  calculateAQI,
  getAQICategory
};
