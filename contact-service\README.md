# AirSense Contact Service

A secure, Firebase-powered backend service for handling contact form submissions in the AirSense air quality monitoring system.

## 🚀 Features

- **Firebase Integration**: Stores contact form data in Firebase Realtime Database
- **Input Validation**: Comprehensive validation for all required fields
- **Email Format Validation**: Ensures proper email format compliance
- **Rate Limiting**: Prevents spam and abuse with configurable limits
- **Security**: Helmet.js for security headers and CORS protection
- **Error Handling**: Detailed error responses and logging
- **Admin Endpoints**: Retrieve contact submissions for admin review

## 📋 Requirements

All fields are mandatory for contact form submission:
- **Name**: 2-100 characters, non-empty string
- **Email**: Valid email format (<EMAIL>)
- **Subject**: 5-200 characters, descriptive subject line
- **Message**: 10-2000 characters, detailed message content

## 🛠️ Setup & Installation

### 1. Install Dependencies
```bash
cd contact-service
npm install
```

### 2. Firebase Configuration
The service uses the existing Firebase configuration from the sensor-data-service:
- **Database URL**: `https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/`
- **Project ID**: `test-2-c0fd4`
- **Service Account**: Uses `../sensor-data-service/serviceAccountKey.json`

### 3. Start Service
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

### 4. Service URL
- **HTTP API**: http://localhost:3005
- **Health Check**: http://localhost:3005/health

## 📡 API Endpoints

### POST /api/contact
Submit a new contact form.

**Request Body:**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "subject": "Question about AirSense",
  "message": "I have a question about the air quality monitoring system..."
}
```

**Success Response (200):**
```json
{
  "success": true,
  "message": "Contact form submitted successfully",
  "contactId": "contact_1641234567890_abc123def",
  "timestamp": "2025-07-10T10:30:00.000Z"
}
```

**Error Response (400):**
```json
{
  "success": false,
  "error": "Validation failed",
  "details": [
    "Name is required and must be a non-empty string",
    "Email must be in a valid format (e.g., <EMAIL>)"
  ]
}
```

### GET /api/contacts
Retrieve all contact submissions (admin use).

**Success Response (200):**
```json
{
  "success": true,
  "contacts": [
    {
      "id": "contact_1641234567890_abc123def",
      "name": "John Doe",
      "email": "<EMAIL>",
      "subject": "Question about AirSense",
      "message": "I have a question...",
      "submittedAt": "2025-07-10T10:30:00.000Z",
      "status": "new"
    }
  ],
  "count": 1
}
```

### GET /health
Health check endpoint.

**Response (200):**
```json
{
  "status": "healthy",
  "service": "airsense-contact-service",
  "timestamp": "2025-07-10T10:30:00.000Z",
  "firebase": "connected"
}
```

## 🔒 Security Features

- **Rate Limiting**: 10 requests per 15 minutes per IP
- **Input Validation**: Comprehensive validation for all fields
- **Data Sanitization**: Automatic trimming and formatting
- **CORS Protection**: Configured for specific origins
- **Security Headers**: Helmet.js for additional security

## 📊 Database Structure

Contact submissions are stored in Firebase under the `contacts` path:

```
contacts/
├── contact_1641234567890_abc123def/
│   ├── name: "John Doe"
│   ├── email: "<EMAIL>"
│   ├── subject: "Question about AirSense"
│   ├── message: "I have a question..."
│   ├── timestamp: 1641234567890
│   ├── submittedAt: "2025-07-10T10:30:00.000Z"
│   ├── status: "new"
│   └── ipAddress: "*************"
└── contact_1641234567891_def456ghi/
    └── ...
```

## 🔧 Configuration

### Environment Variables
- `PORT`: Service port (default: 3005)

### CORS Origins
The service accepts requests from:
- http://localhost:5173-5177 (Main app)
- http://localhost:3001, 3003, 3004 (Admin app)

## 🧪 Testing

```bash
npm test
```

## 📝 Logs

The service provides detailed logging:
- 🚀 Service startup
- 📧 Contact form submissions
- ❌ Validation errors
- 🔥 Firebase connection status

## 🔄 Integration

This service integrates with:
- **React frontend**: Contact form submission
- **Firebase**: Data storage and retrieval
- **Admin interface**: Contact management (future feature)

## 🎯 Usage Example

```javascript
// Frontend integration example
const submitContactForm = async (formData) => {
  try {
    const response = await fetch('http://localhost:3005/api/contact', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(formData)
    });
    
    const result = await response.json();
    
    if (result.success) {
      console.log('Form submitted successfully:', result.contactId);
    } else {
      console.error('Validation errors:', result.details);
    }
  } catch (error) {
    console.error('Submission error:', error);
  }
};
```
