const { FirebaseService } = require('./firebase');

/**
 * Admin Session Management Service
 * Handles admin login/logout tracking and data collection control
 */
class AdminSessionService {
  constructor() {
    this.adminEmail = '<EMAIL>';
    this.adminPassword = 'admin123';
    this.sessionCheckInterval = null;
  }

  /**
   * Authenticate admin credentials
   */
  authenticateAdmin(email, password) {
    return email === this.adminEmail && password === this.adminPassword;
  }

  /**
   * Handle admin login
   */
  async handleAdminLogin(email, password) {
    try {
      if (!this.authenticateAdmin(email, password)) {
        throw new Error('Invalid admin credentials');
      }

      // Set admin session in Firebase
      await FirebaseService.setAdminSession(true, email);
      
      console.log(`👑 Admin logged in: ${email}`);
      return {
        success: true,
        message: 'Admin logged in successfully',
        adminEmail: email,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Admin login failed:', error);
      throw error;
    }
  }

  /**
   * Handle admin logout
   */
  async handleAdminLogout(email = this.adminEmail) {
    try {
      // Clear active location and stop data collection
      await FirebaseService.clearActiveLocation(email);
      
      // Set admin session to logged out
      await FirebaseService.setAdminSession(false, email);
      
      console.log(`👑 Admin logged out: ${email}`);
      return {
        success: true,
        message: 'Admin logged out successfully',
        adminEmail: email,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Admin logout failed:', error);
      throw error;
    }
  }

  /**
   * Handle location selection by admin
   */
  async handleLocationSelection(location, adminEmail = this.adminEmail) {
    try {
      // Verify admin is logged in
      const adminSession = await FirebaseService.getAdminSession();
      if (!adminSession.isLoggedIn) {
        throw new Error('Admin must be logged in to select location');
      }

      // Validate location
      const validLocations = ['deans-office', 'lecture-hall', 'basement'];
      if (!validLocations.includes(location)) {
        throw new Error('Invalid location selected');
      }

      // Map location to Sensor_Data category
      const locationToCategoryMap = {
        'deans-office': "Dean's Office",
        'lecture-hall': 'Lecture Hall',
        'basement': 'Basement'
      };

      const sensorDataCategory = locationToCategoryMap[location];
      if (!sensorDataCategory) {
        throw new Error(`No Sensor_Data category mapping found for location: ${location}`);
      }

      // Set active location and start data collection
      await FirebaseService.setActiveLocation(location, adminEmail);

      // Set the corresponding Sensor_Data category for data duplication
      await FirebaseService.setActiveSensorDataCategory(sensorDataCategory, adminEmail);

      console.log(`📍 Location selected by admin: ${location}`);
      console.log(`📁 Sensor_Data category set to: ${sensorDataCategory}`);

      return {
        success: true,
        message: 'Location selected successfully',
        location: location,
        sensorDataCategory: sensorDataCategory,
        adminEmail: adminEmail,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Location selection failed:', error);
      throw error;
    }
  }

  /**
   * Get current admin session status
   */
  async getSessionStatus() {
    try {
      const adminSession = await FirebaseService.getAdminSession();
      const activeLocation = await FirebaseService.getActiveLocation();
      const dataCollectionStatus = await FirebaseService.getDataCollectionStatus();

      return {
        adminSession: adminSession,
        activeLocation: activeLocation,
        dataCollection: dataCollectionStatus,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      console.error('❌ Error getting session status:', error);
      throw error;
    }
  }

  /**
   * Check if data collection should be active
   */
  async shouldCollectData() {
    try {
      const status = await this.getSessionStatus();
      return status.adminSession.isLoggedIn && 
             status.dataCollection.isActive && 
             status.activeLocation.location !== null;
    } catch (error) {
      console.error('❌ Error checking data collection status:', error);
      return false;
    }
  }

  /**
   * Get the current active location for data storage
   */
  async getActiveLocationForStorage() {
    try {
      const activeLocation = await FirebaseService.getActiveLocation();
      return activeLocation.location;
    } catch (error) {
      console.error('❌ Error getting active location:', error);
      return null;
    }
  }

  /**
   * Handle device restart scenario
   * Returns the last active location if admin is still logged in
   */
  async handleDeviceRestart() {
    try {
      const status = await this.getSessionStatus();
      
      if (status.adminSession.isLoggedIn && status.activeLocation.location) {
        console.log(`📱 Device restart: Using last active location: ${status.activeLocation.location}`);
        return status.activeLocation.location;
      }
      
      console.log('📱 Device restart: No active admin session or location');
      return null;
    } catch (error) {
      console.error('❌ Error handling device restart:', error);
      return null;
    }
  }

  /**
   * Start monitoring admin session (optional - for advanced features)
   */
  startSessionMonitoring(intervalMs = 30000) {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
    }

    this.sessionCheckInterval = setInterval(async () => {
      try {
        const status = await this.getSessionStatus();
        console.log(`🔍 Session check - Admin: ${status.adminSession.isLoggedIn ? 'Online' : 'Offline'}, Location: ${status.activeLocation.location || 'None'}, Data Collection: ${status.dataCollection.isActive ? 'Active' : 'Inactive'}`);
      } catch (error) {
        console.error('❌ Session monitoring error:', error);
      }
    }, intervalMs);

    console.log('🔍 Admin session monitoring started');
  }

  /**
   * Stop session monitoring
   */
  stopSessionMonitoring() {
    if (this.sessionCheckInterval) {
      clearInterval(this.sessionCheckInterval);
      this.sessionCheckInterval = null;
      console.log('🔍 Admin session monitoring stopped');
    }
  }
}

module.exports = AdminSessionService;
