{"name": "airsense-contact-service", "version": "1.0.0", "description": "Firebase-powered contact form service for AirSense air quality monitoring system", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest", "test:api": "node test-api.js"}, "keywords": ["airsense", "contact-form", "firebase", "express", "validation", "backend"], "author": "AirSense Team", "license": "MIT", "dependencies": {"axios": "^1.10.0", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "firebase-admin": "^12.0.0", "helmet": "^7.1.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}, "engines": {"node": ">=16.0.0"}}