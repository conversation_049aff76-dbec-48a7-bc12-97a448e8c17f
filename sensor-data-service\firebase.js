const admin = require('firebase-admin');
const path = require('path');
const { calculateAQI } = require('./aqi-calculator');

// Firebase configuration
const FIREBASE_CONFIG = {
  databaseURL: 'https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/',
  projectId: 'test-2-c0fd4'
};

// Initialize Firebase Admin SDK
let firebaseApp = null;
let database = null;

function initializeFirebase() {
  try {
    if (!firebaseApp) {
      // Load service account key
      const serviceAccount = require('./serviceAccountKey.json');
      
      // Initialize Firebase Admin
      firebaseApp = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        databaseURL: FIREBASE_CONFIG.databaseURL
      });
      
      // Get database reference
      database = admin.database();
      
      console.log('🔥 Firebase Admin SDK initialized successfully');
      console.log(`📊 Database URL: ${FIREBASE_CONFIG.databaseURL}`);
      console.log(`🏗️ Project ID: ${FIREBASE_CONFIG.projectId}`);
    }
    
    return { app: firebaseApp, database };
  } catch (error) {
    console.error('❌ Firebase initialization error:', error);
    throw error;
  }
}

// Get database reference
function getDatabase() {
  if (!database) {
    initializeFirebase();
  }
  return database;
}

// Database path helpers
const DB_PATHS = {
  sensorData: (location) => `sensorData/${location}`,
  sensorDataWithTimestamp: (location, timestamp) => {
    // Ensure Firebase-safe timestamp (replace all invalid characters)
    const safeTimestamp = timestamp.replace(/[.:#$[\]]/g, '_');
    return `sensorData/${location}/${safeTimestamp}`;
  },
  dailyAverage: (location, date) => `sensorData/${location}/dailyAverage/${date}`,
  locations: 'locations',
  // New paths for admin session and location management
  adminSession: 'adminSession',
  activeLocation: 'activeLocation',
  dataCollectionStatus: 'dataCollectionStatus',
  locationHistory: 'locationHistory',
  // Active Sensor_Data category selection
  activeSensorDataCategory: 'activeSensorDataCategory',
  // New Sensor_Data section with categorized locations
  sensorDataSection: 'Sensor_Data',
  sensorDataCategory: (category) => `Sensor_Data/${category}`,
  sensorDataCategoryWithTimestamp: (category, timestamp) => {
    const safeTimestamp = timestamp.replace(/[.:#$[\]]/g, '_');
    return `Sensor_Data/${category}/${safeTimestamp}`;
  },
  sensorDataCategoryDailyAverage: (category, date) => `Sensor_Data/${category}/dailyAverage/${date}`
};

// Utility functions for data operations
const FirebaseService = {
  // Write sensor data
  async writeSensorData(location, timestamp, data) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorDataWithTimestamp(location, timestamp));

      // Calculate AQI if not provided
      const calculatedAQI = data.aqi || calculateAQI(data);

      await ref.set({
        ...data,
        aqi: calculatedAQI,
        timestamp: timestamp,
        location: location,
        createdAt: admin.database.ServerValue.TIMESTAMP
      });
      console.log(`📝 Sensor data written for ${location} at ${timestamp} with AQI: ${calculatedAQI}`);
      return true;
    } catch (error) {
      console.error('❌ Error writing sensor data:', error);
      throw error;
    }
  },

  // Read sensor data for a location
  async readSensorData(location, limit = 100) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorData(location));
      
      // Get latest data, excluding dailyAverage
      const snapshot = await ref.orderByKey()
        .limitToLast(limit)
        .once('value');
      
      const data = snapshot.val();
      if (!data) return [];
      
      // Filter out dailyAverage entries and convert to array
      const sensorReadings = Object.entries(data)
        .filter(([key]) => key !== 'dailyAverage')
        .map(([timestamp, reading]) => ({
          timestamp,
          ...reading
        }))
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
      
      return sensorReadings;
    } catch (error) {
      console.error('❌ Error reading sensor data:', error);
      throw error;
    }
  },

  // Write daily average
  async writeDailyAverage(location, date, averageData) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.dailyAverage(location, date));
      await ref.set({
        ...averageData,
        date: date,
        location: location,
        calculatedAt: admin.database.ServerValue.TIMESTAMP
      });
      console.log(`📊 Daily average written for ${location} on ${date}`);
      return true;
    } catch (error) {
      console.error('❌ Error writing daily average:', error);
      throw error;
    }
  },

  // Read daily averages for a location
  async readDailyAverages(location, days = 30) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.dailyAverage(location, ''));
      
      const snapshot = await ref.limitToLast(days).once('value');
      const data = snapshot.val();
      
      if (!data) return [];
      
      return Object.entries(data).map(([date, average]) => ({
        date,
        ...average
      })).sort((a, b) => new Date(b.date) - new Date(a.date));
    } catch (error) {
      console.error('❌ Error reading daily averages:', error);
      throw error;
    }
  },

  // Get latest sensor reading for a location
  async getLatestReading(location) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorData(location));
      
      const snapshot = await ref.orderByKey()
        .limitToLast(1)
        .once('value');
      
      const data = snapshot.val();
      if (!data) return null;
      
      // Get the latest entry (excluding dailyAverage)
      const entries = Object.entries(data).filter(([key]) => key !== 'dailyAverage');
      if (entries.length === 0) return null;
      
      const [timestamp, reading] = entries[0];
      return {
        timestamp,
        ...reading
      };
    } catch (error) {
      console.error('❌ Error getting latest reading:', error);
      throw error;
    }
  },

  // Listen to real-time updates
  listenToSensorData(location, callback) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorData(location));
      
      ref.on('child_added', (snapshot) => {
        const key = snapshot.key;
        if (key !== 'dailyAverage') {
          const data = snapshot.val();
          callback('added', { timestamp: key, ...data });
        }
      });
      
      ref.on('child_changed', (snapshot) => {
        const key = snapshot.key;
        if (key !== 'dailyAverage') {
          const data = snapshot.val();
          callback('changed', { timestamp: key, ...data });
        }
      });
      
      console.log(`👂 Listening to sensor data for ${location}`);
      return ref;
    } catch (error) {
      console.error('❌ Error setting up listener:', error);
      throw error;
    }
  },

  // Stop listening
  stopListening(ref) {
    if (ref) {
      ref.off();
      console.log('🔇 Stopped listening to Firebase updates');
    }
  },

  // Admin Session Management Functions

  // Set admin session status (login/logout)
  async setAdminSession(isLoggedIn, adminEmail = '<EMAIL>') {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.adminSession);
      const sessionData = {
        isLoggedIn: isLoggedIn,
        adminEmail: adminEmail,
        timestamp: admin.database.ServerValue.TIMESTAMP,
        lastActivity: new Date().toISOString()
      };

      await ref.set(sessionData);
      console.log(`👑 Admin session ${isLoggedIn ? 'started' : 'ended'} for ${adminEmail}`);
      return true;
    } catch (error) {
      console.error('❌ Error setting admin session:', error);
      throw error;
    }
  },

  // Get admin session status
  async getAdminSession() {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.adminSession);
      const snapshot = await ref.once('value');
      const sessionData = snapshot.val();

      if (!sessionData) {
        return { isLoggedIn: false, adminEmail: null, timestamp: null };
      }

      return sessionData;
    } catch (error) {
      console.error('❌ Error getting admin session:', error);
      throw error;
    }
  },

  // Set active location (admin only)
  async setActiveLocation(location, adminEmail = '<EMAIL>') {
    try {
      const db = getDatabase();

      // Check if admin is logged in
      const adminSession = await this.getAdminSession();
      if (!adminSession.isLoggedIn) {
        throw new Error('Admin must be logged in to set location');
      }

      // Set active location
      const activeLocationRef = db.ref(DB_PATHS.activeLocation);
      const locationData = {
        location: location,
        setBy: adminEmail,
        timestamp: admin.database.ServerValue.TIMESTAMP,
        setAt: new Date().toISOString()
      };

      await activeLocationRef.set(locationData);

      // Add to location history
      const historyRef = db.ref(DB_PATHS.locationHistory).push();
      await historyRef.set({
        ...locationData,
        action: 'location_set'
      });

      // Enable data collection
      await this.setDataCollectionStatus(true, location, adminEmail);

      console.log(`📍 Active location set to: ${location} by ${adminEmail}`);
      return true;
    } catch (error) {
      console.error('❌ Error setting active location:', error);
      throw error;
    }
  },

  // Get active location
  async getActiveLocation() {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.activeLocation);
      const snapshot = await ref.once('value');
      const locationData = snapshot.val();

      if (!locationData) {
        return { location: null, setBy: null, timestamp: null };
      }

      return locationData;
    } catch (error) {
      console.error('❌ Error getting active location:', error);
      throw error;
    }
  },

  // Set data collection status
  async setDataCollectionStatus(isActive, location = null, adminEmail = '<EMAIL>') {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.dataCollectionStatus);
      const statusData = {
        isActive: isActive,
        location: location,
        controlledBy: adminEmail,
        timestamp: admin.database.ServerValue.TIMESTAMP,
        lastUpdated: new Date().toISOString()
      };

      await ref.set(statusData);
      console.log(`📊 Data collection ${isActive ? 'started' : 'stopped'} for location: ${location || 'none'}`);
      return true;
    } catch (error) {
      console.error('❌ Error setting data collection status:', error);
      throw error;
    }
  },

  // Get data collection status
  async getDataCollectionStatus() {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.dataCollectionStatus);
      const snapshot = await ref.once('value');
      const statusData = snapshot.val();

      if (!statusData) {
        return { isActive: false, location: null, controlledBy: null };
      }

      return statusData;
    } catch (error) {
      console.error('❌ Error getting data collection status:', error);
      throw error;
    }
  },

  // Clear active location and stop data collection (admin logout)
  async clearActiveLocation(adminEmail = '<EMAIL>') {
    try {
      const db = getDatabase();

      // Add to location history
      const historyRef = db.ref(DB_PATHS.locationHistory).push();
      await historyRef.set({
        location: null,
        setBy: adminEmail,
        timestamp: admin.database.ServerValue.TIMESTAMP,
        setAt: new Date().toISOString(),
        action: 'location_cleared'
      });

      // Clear active location
      const activeLocationRef = db.ref(DB_PATHS.activeLocation);
      await activeLocationRef.remove();

      // Stop data collection
      await this.setDataCollectionStatus(false, null, adminEmail);

      console.log(`🚫 Active location cleared and data collection stopped by ${adminEmail}`);
      return true;
    } catch (error) {
      console.error('❌ Error clearing active location:', error);
      throw error;
    }
  },

  // Enhanced sensor data writing with location validation
  async writeSensorDataWithLocationCheck(sensorData, targetLocation, deviceLocation = null) {
    try {
      // Get current timestamp in ISO format
      const timestamp = new Date().toISOString();
      const firebaseTimestamp = timestamp.replace(/[.:#$[\]]/g, '_');

      // Enhanced sensor data with location info - without deviceLocation
      const enhancedData = {
        ...sensorData,
        timestamp: timestamp,
        location: targetLocation,
        dataCollectionSession: collectionStatus.timestamp,
        // deviceLocation removed
        createdAt: admin.database.ServerValue.TIMESTAMP
      };

      // Store the data
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorDataWithTimestamp(targetLocation, firebaseTimestamp));
      await ref.set(enhancedData);

      console.log(`📝 Sensor data stored for location: ${targetLocation} at ${timestamp}`);
      return { success: true, location: targetLocation, timestamp: timestamp };

    } catch (error) {
      console.error('❌ Error writing sensor data with location check:', error);
      throw error;
    }
  },

  // New Sensor_Data section methods
  // Initialize Sensor_Data section with categories
  async initializeSensorDataSection() {
    try {
      const db = getDatabase();
      const categories = ['Lecture Hall', 'Basement', "Dean's Office"];

      for (const category of categories) {
        const categoryRef = db.ref(DB_PATHS.sensorDataCategory(category));
        const snapshot = await categoryRef.once('value');

        if (!snapshot.exists()) {
          // Initialize category with metadata
          await categoryRef.child('_metadata').set({
            categoryName: category,
            createdAt: admin.database.ServerValue.TIMESTAMP,
            description: `Sensor data for ${category}`,
            lastUpdated: admin.database.ServerValue.TIMESTAMP
          });
          console.log(`📁 Initialized Sensor_Data category: ${category}`);
        }
      }

      console.log('✅ Sensor_Data section initialized with all categories');
      return true;
    } catch (error) {
      console.error('❌ Error initializing Sensor_Data section:', error);
      throw error;
    }
  },

  // Write sensor data to Sensor_Data section
  async writeSensorDataToCategory(category, timestamp, data) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorDataCategoryWithTimestamp(category, timestamp));
      await ref.set({
        ...data,
        timestamp: timestamp,
        category: category,
        createdAt: admin.database.ServerValue.TIMESTAMP
      });
      console.log(`📝 Sensor data written to Sensor_Data/${category} at ${timestamp}`);
      return true;
    } catch (error) {
      console.error('❌ Error writing sensor data to category:', error);
      throw error;
    }
  },

  // Read sensor data from Sensor_Data category
  async readSensorDataFromCategory(category, limit = 100) {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorDataCategory(category));

      // Get latest data, excluding metadata and dailyAverage
      const snapshot = await ref.orderByKey()
        .limitToLast(limit + 2) // +2 to account for metadata and dailyAverage
        .once('value');

      const data = snapshot.val();
      if (!data) return [];

      // Filter out metadata and dailyAverage entries and convert to array
      const sensorReadings = Object.entries(data)
        .filter(([key]) => key !== '_metadata' && key !== 'dailyAverage')
        .map(([timestamp, reading]) => ({
          timestamp,
          ...reading
        }))
        .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

      return sensorReadings;
    } catch (error) {
      console.error('❌ Error reading sensor data from category:', error);
      throw error;
    }
  },

  // Read sensor data from hierarchical structure (Sensor_Data/{category}/{date}/{time})
  async readSensorDataFromHierarchy(category, limit = 100) {
    try {
      const db = getDatabase();
      const ref = db.ref(`Sensor_Data/${category}`);

      const snapshot = await ref.once('value');
      const data = snapshot.val();

      if (!data) {
        console.log(`📊 No data found in hierarchical structure for ${category}`);
        return [];
      }

      // Flatten the hierarchical structure and collect all readings
      const allReadings = [];

      // Iterate through dates
      Object.entries(data).forEach(([date, dateData]) => {
        if (date === '_metadata' || date === 'dailyAverage') return;

        // Iterate through times for each date
        if (typeof dateData === 'object' && dateData !== null) {
          Object.entries(dateData).forEach(([time, reading]) => {
            if (typeof reading === 'object' && reading !== null) {
              // Create a proper timestamp from date and time
              const timestamp = `${date} ${time}`;
              allReadings.push({
                timestamp,
                ...reading
              });
            }
          });
        }
      });

      console.log(`📊 Total readings collected from hierarchical structure for ${category}: ${allReadings.length}`);

      // Sort by timestamp (newest first) and limit results
      // Handle different timestamp formats properly
      const sortedReadings = allReadings
        .sort((a, b) => {
          // Normalize timestamps to handle multiple formats:
          // "2025-07-24 10:24:42" -> "2025-07-24T10:24:42"
          // "2025-07-12T09_33_16_817Z" -> "2025-07-12T09:33:16.817Z"
          const normalizeTimestamp = (ts) => {
            return ts
              .replace(' ', 'T')  // Replace space with T
              .replace(/_/g, ':') // Replace underscores with colons
              .replace(/(\d{2}):(\d{2}):(\d{2}):(\d{3})Z/, '$1:$2:$3.$4Z'); // Fix milliseconds format
          };

          const dateA = new Date(normalizeTimestamp(a.timestamp));
          const dateB = new Date(normalizeTimestamp(b.timestamp));



          return dateB.getTime() - dateA.getTime();
        })
        .slice(0, limit);

      if (sortedReadings.length > 0) {
        console.log(`📊 Latest reading timestamp: ${sortedReadings[0].timestamp}`);
      }

      console.log(`📊 Found ${sortedReadings.length} readings from hierarchical structure for ${category}`);
      return sortedReadings;
    } catch (error) {
      console.error('❌ Error reading sensor data from hierarchy:', error);
      throw error;
    }
  },

  // Get latest reading from hierarchical structure
  async getLatestReadingFromHierarchy(category) {
    try {
      const readings = await this.readSensorDataFromHierarchy(category, 1);
      return readings.length > 0 ? readings[0] : null;
    } catch (error) {
      console.error('❌ Error getting latest reading from hierarchy:', error);
      throw error;
    }
  },

  // Get all categories in Sensor_Data section
  async getSensorDataCategories() {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.sensorDataSection);
      const snapshot = await ref.once('value');

      if (!snapshot.exists()) {
        return [];
      }

      const data = snapshot.val();
      return Object.keys(data);
    } catch (error) {
      console.error('❌ Error getting sensor data categories:', error);
      throw error;
    }
  },

  // Set active Sensor_Data category for data storage
  async setActiveSensorDataCategory(category, adminEmail = '<EMAIL>') {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.activeSensorDataCategory);

      const categoryData = {
        category: category,
        setBy: adminEmail,
        timestamp: admin.database.ServerValue.TIMESTAMP,
        setAt: new Date().toISOString()
      };

      await ref.set(categoryData);
      console.log(`📁 Active Sensor_Data category set to: ${category}`);
      return categoryData;
    } catch (error) {
      console.error('❌ Error setting active Sensor_Data category:', error);
      throw error;
    }
  },

  // Get active Sensor_Data category
  async getActiveSensorDataCategory() {
    try {
      const db = getDatabase();
      const ref = db.ref(DB_PATHS.activeSensorDataCategory);
      const snapshot = await ref.once('value');

      if (!snapshot.exists()) {
        return { category: null, setBy: null, setAt: null };
      }

      return snapshot.val();
    } catch (error) {
      console.error('❌ Error getting active Sensor_Data category:', error);
      return { category: null, setBy: null, setAt: null };
    }
  },

  // Hall Data Auto-Duplication System
  // Listen to new data in hall section and auto-duplicate to Sensor_Data
  hallDataListener: null,

  async startHallDataListener() {
    try {
      const db = getDatabase();

      // Listen to hall/data for new entries
      const hallDataRef = db.ref('hall/data');

      this.hallDataListener = hallDataRef.on('child_added', async (dateSnapshot) => {
        const date = dateSnapshot.key; // e.g., "2025-07-21"
        const dateData = dateSnapshot.val();

        console.log(`🔥 New hall data detected for date: ${date}`);

        // Listen to time entries within this date
        const dateRef = db.ref(`hall/data/${date}`);
        dateRef.on('child_added', async (timeSnapshot) => {
          const timeKey = timeSnapshot.key; // e.g., "12:07:29"
          const sensorData = timeSnapshot.val();

          console.log(`📊 New hall time entry: ${date} ${timeKey}`);

          try {
            // Get current active location to determine target category
            const activeLocation = await this.getActiveLocation();

            if (!activeLocation.location) {
              console.log('⚠️ No active location set, skipping hall data duplication');
              return;
            }

            // Map location to Sensor_Data category
            const locationToCategoryMap = {
              'lecture-hall': 'Lecture Hall',
              'basement': 'Basement',
              'deans-office': "Dean's Office"
            };

            const targetCategory = locationToCategoryMap[activeLocation.location];

            if (!targetCategory) {
              console.log(`⚠️ Unknown location: ${activeLocation.location}, skipping duplication`);
              return;
            }

            // Use the exact timestamp from hall data
            const originalTimestamp = sensorData.timestamp || `${date} ${timeKey}`;

            // Duplicate to Sensor_Data with hierarchical structure
            await this.writeSensorDataToCategoryWithHierarchy(
              targetCategory,
              sensorData,
              originalTimestamp
            );

            console.log(`✅ Hall data auto-duplicated to Sensor_Data/${targetCategory} for ${originalTimestamp}`);

          } catch (error) {
            console.error(`❌ Error auto-duplicating hall data for ${date} ${timeKey}:`, error.message);
          }
        });
      });

      console.log('🔥 Hall data auto-duplication listener started');
      console.log('📊 Monitoring hall/data for new sensor readings...');
      console.log('🔄 New data will be automatically duplicated to Sensor_Data based on active location');

      return true;
    } catch (error) {
      console.error('❌ Error starting hall data listener:', error);
      throw error;
    }
  },

  async stopHallDataListener() {
    try {
      if (this.hallDataListener) {
        const db = getDatabase();
        const hallDataRef = db.ref('hall/data');
        hallDataRef.off('child_added', this.hallDataListener);
        this.hallDataListener = null;
        console.log('🔇 Hall data auto-duplication listener stopped');
      }
    } catch (error) {
      console.error('❌ Error stopping hall data listener:', error);
    }
  },

  // Write sensor data to Sensor_Data section with date/time hierarchy using exact hall timestamps
  async writeSensorDataToCategoryWithHierarchy(category, sensorData, originalTimestamp = null) {
    try {
      const db = getDatabase();

      let date, time;

      if (originalTimestamp && typeof originalTimestamp === 'string') {
        // Use exact timestamp from hall data: "2025-07-20 00:11:09"
        if (originalTimestamp.includes(' ')) {
          [date, time] = originalTimestamp.split(' ');
          console.log(`🕐 Using exact hall timestamp: ${originalTimestamp} → ${date}/${time}`);
        } else if (originalTimestamp.includes('T')) {
          // Handle ISO format: "2025-07-20T00:11:09"
          const parts = originalTimestamp.split('T');
          date = parts[0];
          time = parts[1].split('.')[0]; // Remove milliseconds if present
          console.log(`🕐 Using ISO timestamp: ${originalTimestamp} → ${date}/${time}`);
        } else {
          // Fallback to current time
          console.log(`⚠️ Unrecognized timestamp format: ${originalTimestamp}, using current time`);
          const now = new Date();
          date = now.toISOString().split('T')[0];
          time = now.toTimeString().split(' ')[0];
        }
      } else {
        // Fallback to current time
        console.log(`⚠️ No original timestamp provided, using current time`);
        const now = new Date();
        date = now.toISOString().split('T')[0];
        time = now.toTimeString().split(' ')[0];
      }

      // Path: Sensor_Data/{category}/{date}/{time}
      const hierarchyPath = `Sensor_Data/${category}/${date}/${time}`;
      const ref = db.ref(hierarchyPath);

      // Calculate AQI if not provided
      const calculatedAQI = sensorData.aqi || calculateAQI(sensorData);

      // Prepare data with the exact structure from the image
      const hierarchicalData = {
        co2: sensorData.co2 || 0,
        gasResistance: sensorData.gasResistance || 0,
        humidity: sensorData.humidity || 0,
        pm10: sensorData.pm10 || 0,
        pm25: sensorData.pm25 || 0,
        pressure: sensorData.pressure || 0,
        temperature: sensorData.temperature || 0,
        timestamp: originalTimestamp || `${date} ${time}`,
        // Additional metadata
        category: category,
        aqi: calculatedAQI,
        createdAt: admin.database.ServerValue.TIMESTAMP
      };

      await ref.set(hierarchicalData);
      console.log(`📝 Sensor data written to ${hierarchyPath} with exact timestamp: ${originalTimestamp || `${date} ${time}`}`);
      return { success: true, path: hierarchyPath, data: hierarchicalData };
    } catch (error) {
      console.error('❌ Error writing sensor data with hierarchy:', error);
      throw error;
    }
  }
};

module.exports = {
  initializeFirebase,
  getDatabase,
  FirebaseService,
  DB_PATHS,
  FIREBASE_CONFIG
};






