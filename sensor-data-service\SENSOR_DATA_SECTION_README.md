# Sensor_Data Section Documentation

## Overview

The **Sensor_Data** section is a new Firebase database structure that organizes sensor data into three specific categories:

- **Lecture Hall**
- **Basement** 
- **Dean's Office**

This section provides a categorized approach to storing and retrieving sensor data, separate from the existing location-based `sensorData` structure.

## Database Structure

```
Firebase Database
├── Sensor_Data/
│   ├── Lecture Hall/
│   │   ├── _metadata/
│   │   │   ├── categoryName: "Lecture Hall"
│   │   │   ├── createdAt: [timestamp]
│   │   │   ├── description: "Sensor data for Lecture Hall"
│   │   │   └── lastUpdated: [timestamp]
│   │   ├── [timestamp]/     # Individual sensor readings
│   │   │   ├── temperature: [number]
│   │   │   ├── humidity: [number]
│   │   │   ├── co2: [number]
│   │   │   ├── pm25: [number]
│   │   │   ├── pm10: [number]
│   │   │   ├── gasResistance: [number]
│   │   │   ├── aqi: [calculated]
│   │   │   ├── category: "Lecture Hall"
│   │   │   ├── timestamp: [ISO string]
│   │   │   └── createdAt: [Firebase timestamp]
│   │   └── dailyAverage/    # Future: Daily averages
│   ├── Basement/
│   │   └── [same structure as above]
│   └── Dean's Office/
│       └── [same structure as above]
```

## API Endpoints

### 1. Get All Categories
```http
GET /api/sensor-data-section/categories
```

**Response:**
```json
{
  "success": true,
  "categories": ["Lecture Hall", "Basement", "Dean's Office"],
  "count": 3
}
```

### 2. Get Sensor Data for a Category
```http
GET /api/sensor-data-section/{category}?limit=100
```

**Parameters:**
- `category`: Category name (URL encoded if contains spaces)
- `limit`: Optional, number of records to retrieve (default: 100)

**Example:**
```http
GET /api/sensor-data-section/Lecture%20Hall?limit=50
```

**Response:**
```json
{
  "success": true,
  "category": "Lecture Hall",
  "count": 2,
  "data": [
    {
      "timestamp": "2025-07-20T04:02:34.262Z",
      "aqi": 0,
      "category": "Lecture Hall",
      "co2": 900,
      "createdAt": 1752984156424,
      "gasResistance": 300,
      "humidity": 50,
      "pm10": 30,
      "pm25": 20,
      "temperature": 25.5
    }
  ]
}
```

### 3. Post Sensor Data to a Category
```http
POST /api/sensor-data-section/{category}
Content-Type: application/json
```

**Request Body:**
```json
{
  "temperature": 25.5,
  "humidity": 50.0,
  "co2": 900,
  "pm25": 20.0,
  "pm10": 30.0,
  "gasResistance": 300.0
}
```

**Response:**
```json
{
  "success": true,
  "category": "Lecture Hall",
  "timestamp": "2025-07-20T04:02:34.262Z",
  "aqi": 0,
  "message": "Sensor data stored in Sensor_Data/Lecture Hall"
}
```

### 4. Initialize Sensor_Data Section (Admin)
```http
POST /api/sensor-data-section/initialize
```

**Response:**
```json
{
  "success": true,
  "message": "Sensor_Data section initialized successfully",
  "categories": ["Lecture Hall", "Basement", "Dean's Office"],
  "count": 3
}
```

## Data Validation

All sensor data must include the following required fields:
- `temperature` (number)
- `humidity` (number) 
- `co2` (number)
- `pm25` (number)
- `pm10` (number)
- `gasResistance` (number)

The AQI (Air Quality Index) is automatically calculated based on PM2.5 and PM10 values.

## Usage Examples

### Using PowerShell/Windows

```powershell
# Get all categories
Invoke-WebRequest -Uri "http://localhost:3003/api/sensor-data-section/categories" -Method GET

# Get data from Lecture Hall
Invoke-WebRequest -Uri "http://localhost:3003/api/sensor-data-section/Lecture%20Hall" -Method GET

# Post sensor data to Basement
Invoke-WebRequest -Uri "http://localhost:3003/api/sensor-data-section/Basement" -Method POST -ContentType "application/json" -Body '{"temperature": 22.0, "humidity": 45.0, "co2": 800, "pm25": 15.0, "pm10": 25.0, "gasResistance": 250.0}'
```

### Using curl/Linux

```bash
# Get all categories
curl -X GET http://localhost:3003/api/sensor-data-section/categories

# Get data from Dean's Office
curl -X GET "http://localhost:3003/api/sensor-data-section/Dean's%20Office"

# Post sensor data to Lecture Hall
curl -X POST http://localhost:3003/api/sensor-data-section/Lecture%20Hall \
  -H "Content-Type: application/json" \
  -d '{"temperature": 24.0, "humidity": 55.0, "co2": 950, "pm25": 18.0, "pm10": 28.0, "gasResistance": 280.0}'
```

## Firebase Database URL

Access the Firebase console at:
https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/

Navigate to `Sensor_Data/` to view the categorized sensor data.

## Initialization

The Sensor_Data section was initialized with sample data for demonstration. To reinitialize or add new categories, use the initialization script:

```bash
node initialize-sensor-data-section.js
```

## Cleanup History

**2025-07-20**: All mock/sample data was removed from the Sensor_Data section while preserving the category structure and metadata. The cleanup script `cleanup-sensor-data-mock.js` was used to:

- ✅ Remove all sensor data entries from all categories
- ✅ Preserve category structure (`_metadata` and `dailyAverage` folders)
- ✅ Update metadata with cleanup timestamps
- ✅ Verify cleanup completion through API testing

**Current Status**: All categories (Lecture Hall, Basement, Dean's Office) are clean and ready for new sensor data.

## Notes

- All timestamps are stored in ISO 8601 format
- Firebase timestamps are used for `createdAt` fields
- Category names are case-sensitive
- URL encode category names when making HTTP requests (spaces become `%20`)
- The system automatically calculates AQI values
- Data is sorted by timestamp in descending order (newest first)
- Mock data has been completely removed as of 2025-07-20
