#!/usr/bin/env node

const { FirebaseService, getDatabase } = require('./firebase');

async function checkSensorDataStructure() {
  try {
    console.log('🔍 Checking Sensor_Data structure...\n');
    
    const db = getDatabase();
    
    // Check Sensor_Data structure
    const sensorDataRef = db.ref('Sensor_Data');
    const snapshot = await sensorDataRef.once('value');
    
    if (snapshot.exists()) {
      const data = snapshot.val();
      console.log('📁 Available categories:', Object.keys(data));
      
      // Check each category
      for (const category of Object.keys(data)) {
        if (category === '_metadata') continue;
        
        console.log(`\n📂 Category: ${category}`);
        const categoryData = data[category];
        
        if (categoryData && typeof categoryData === 'object') {
          const dates = Object.keys(categoryData).filter(key => key.startsWith('2025-07-'));
          console.log(`   📅 Dates: ${dates.length > 0 ? dates.join(', ') : 'None'}`);
          
          if (dates.length > 0) {
            const latestDate = dates.sort().pop();
            const timeEntries = Object.keys(categoryData[latestDate]);
            console.log(`   ⏰ Times for ${latestDate}: ${timeEntries.length} entries`);
            
            if (timeEntries.length > 0) {
              // Show sample data
              const sampleTime = timeEntries[0];
              const sampleData = categoryData[latestDate][sampleTime];
              console.log(`   📊 Sample data (${sampleTime}):`, {
                temperature: sampleData.temperature,
                humidity: sampleData.humidity,
                co2: sampleData.co2,
                timestamp: sampleData.timestamp
              });
            }
          }
        }
      }
    } else {
      console.log('❌ No Sensor_Data found');
    }
    
    // Also check active category
    console.log('\n🔍 Checking active Sensor_Data category...');
    const activeCategory = await FirebaseService.getActiveSensorDataCategory();
    console.log('📁 Active category:', activeCategory);
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkSensorDataStructure();
