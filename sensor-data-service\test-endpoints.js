const axios = require('axios');

const BASE_URL = 'http://localhost:3003';

// Test data for ESP32 simulation
const generateTestSensorData = () => ({
  temperature: 22.5 + (Math.random() - 0.5) * 4, // 20.5 - 24.5°C
  humidity: 45 + (Math.random() - 0.5) * 20, // 35 - 55%
  co2: 800 + Math.random() * 400, // 800 - 1200 ppm
  pm25: 15 + Math.random() * 20, // 15 - 35 μg/m³
  pm10: 20 + Math.random() * 25, // 20 - 45 μg/m³
  voc: 200 + Math.random() * 300 // 200 - 500 ppb
});

// Test functions
async function testHealthCheck() {
  try {
    console.log('🏥 Testing health check...');
    const response = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Health check failed:', error.message);
    return false;
  }
}

async function testSensorDataIngestion() {
  try {
    console.log('📊 Testing sensor data ingestion...');
    const locations = ['deans-office', 'lecture-hall', 'basement'];
    
    for (const location of locations) {
      const testData = generateTestSensorData();
      console.log(`📍 Sending test data for ${location}:`, testData);
      
      const response = await axios.post(`${BASE_URL}/api/sensor-data/${location}`, testData);
      console.log(`✅ Data stored for ${location}:`, response.data);
      
      // Wait a bit between requests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    return true;
  } catch (error) {
    console.error('❌ Sensor data ingestion failed:', error.response?.data || error.message);
    return false;
  }
}

async function testDataRetrieval() {
  try {
    console.log('📖 Testing data retrieval...');
    const locations = ['deans-office', 'lecture-hall', 'basement'];
    
    for (const location of locations) {
      // Test latest data
      console.log(`📍 Getting latest data for ${location}...`);
      const latestResponse = await axios.get(`${BASE_URL}/api/sensor-data/${location}/latest`);
      console.log(`✅ Latest data for ${location}:`, latestResponse.data);
      
      // Test historical data
      console.log(`📍 Getting historical data for ${location}...`);
      const historyResponse = await axios.get(`${BASE_URL}/api/sensor-data/${location}/history?limit=5`);
      console.log(`✅ Historical data for ${location}: ${historyResponse.data.count} records`);
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    return true;
  } catch (error) {
    console.error('❌ Data retrieval failed:', error.response?.data || error.message);
    return false;
  }
}

async function testDailyAverageCalculation() {
  try {
    console.log('📊 Testing daily average calculation...');
    
    // Calculate daily averages for yesterday
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const dateStr = yesterday.toISOString().split('T')[0];
    
    console.log(`📅 Calculating daily averages for ${dateStr}...`);
    const response = await axios.post(`${BASE_URL}/api/daily-averages/calculate`, {
      date: dateStr
    });
    console.log('✅ Daily averages calculated:', response.data);
    
    // Check calculation status
    const statusResponse = await axios.get(`${BASE_URL}/api/daily-averages/status`);
    console.log('✅ Daily average service status:', statusResponse.data);
    
    return true;
  } catch (error) {
    console.error('❌ Daily average calculation failed:', error.response?.data || error.message);
    return false;
  }
}

async function testDailyAverageRetrieval() {
  try {
    console.log('📈 Testing daily average retrieval...');
    const locations = ['deans-office', 'lecture-hall', 'basement'];
    
    for (const location of locations) {
      console.log(`📍 Getting daily averages for ${location}...`);
      const response = await axios.get(`${BASE_URL}/api/sensor-data/${location}/daily-averages?days=7`);
      console.log(`✅ Daily averages for ${location}: ${response.data.count} records`);
      
      if (response.data.count > 0) {
        const latest = response.data.data[0];
        console.log(`   Latest average: Temp=${latest.avgTemperature?.toFixed(1)}°C, AQI=${latest.avgAQI?.toFixed(0)}`);
      }
      
      await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    return true;
  } catch (error) {
    console.error('❌ Daily average retrieval failed:', error.response?.data || error.message);
    return false;
  }
}

async function simulateESP32Data() {
  try {
    console.log('🤖 Simulating ESP32 sensor data...');
    const locations = ['deans-office', 'lecture-hall', 'basement'];
    
    // Send 10 readings for each location
    for (let i = 0; i < 10; i++) {
      for (const location of locations) {
        const testData = generateTestSensorData();
        
        try {
          await axios.post(`${BASE_URL}/api/sensor-data/${location}`, testData);
          console.log(`📊 Sent reading ${i + 1} for ${location} - AQI: ${Math.round(testData.pm25 * 2)}`);
        } catch (error) {
          console.error(`❌ Failed to send data for ${location}:`, error.message);
        }
        
        // Small delay between locations
        await new Promise(resolve => setTimeout(resolve, 200));
      }
      
      // Delay between rounds
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    console.log('✅ ESP32 simulation completed');
    return true;
  } catch (error) {
    console.error('❌ ESP32 simulation failed:', error.message);
    return false;
  }
}

// Main test runner
async function runAllTests() {
  console.log('🧪 Starting Firebase Integration Tests...\n');
  
  const tests = [
    { name: 'Health Check', fn: testHealthCheck },
    { name: 'ESP32 Data Simulation', fn: simulateESP32Data },
    { name: 'Sensor Data Ingestion', fn: testSensorDataIngestion },
    { name: 'Data Retrieval', fn: testDataRetrieval },
    { name: 'Daily Average Calculation', fn: testDailyAverageCalculation },
    { name: 'Daily Average Retrieval', fn: testDailyAverageRetrieval }
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of tests) {
    console.log(`\n${'='.repeat(50)}`);
    console.log(`🧪 Running: ${test.name}`);
    console.log(`${'='.repeat(50)}`);
    
    try {
      const result = await test.fn();
      if (result) {
        passed++;
        console.log(`✅ ${test.name} PASSED`);
      } else {
        failed++;
        console.log(`❌ ${test.name} FAILED`);
      }
    } catch (error) {
      failed++;
      console.log(`❌ ${test.name} FAILED:`, error.message);
    }
    
    // Wait between tests
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log(`\n${'='.repeat(50)}`);
  console.log('🏁 TEST RESULTS');
  console.log(`${'='.repeat(50)}`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📊 Total: ${passed + failed}`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Firebase integration is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Please check the Firebase configuration and server status.');
  }
}

// Run tests if called directly
if (require.main === module) {
  runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error);
    process.exit(1);
  });
}

module.exports = {
  runAllTests,
  testHealthCheck,
  testSensorDataIngestion,
  testDataRetrieval,
  testDailyAverageCalculation,
  simulateESP32Data
};
