{"name": "airsense-location-service", "version": "1.0.0", "description": "Multi-machine location synchronization service for AirSense", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "keywords": ["airsense", "location", "sync", "websocket"], "author": "AirSense Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "ws": "^8.14.2", "fs-extra": "^11.1.1"}, "devDependencies": {"nodemon": "^3.0.1"}}