// Location Synchronization Service Integration
// Provides multi-machine location sync with graceful fallback to localStorage

interface LocationData {
  location: string;
  lastUpdated: string | null;
  updatedBy: string | null;
}

interface LocationResponse {
  success: boolean;
  location: string;
  lastUpdated: string | null;
  updatedBy: string | null;
  error?: string;
}

class LocationSyncService {
  private static instance: LocationSyncService;
  private ws: WebSocket | null = null;
  private serviceUrl = 'http://localhost:3002';
  private wsUrl = 'ws://localhost:3002';
  private isServiceAvailable = false;
  private listeners: ((location: string) => void)[] = [];
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 3000;
  private initializationPromise: Promise<void> | null = null;

  private constructor() {
    this.initializationPromise = this.initializeService();
  }

  // Initialize service with proper async handling
  private async initializeService(): Promise<void> {
    await this.checkServiceAvailability();
    this.initializeWebSocket();
  }

  // Ensure service is initialized before operations
  private async ensureInitialized(): Promise<void> {
    if (this.initializationPromise) {
      await this.initializationPromise;
    }
  }

  public static getInstance(): LocationSyncService {
    if (!LocationSyncService.instance) {
      LocationSyncService.instance = new LocationSyncService();
    }
    return LocationSyncService.instance;
  }

  // Check if the location service is available
  private async checkServiceAvailability(): Promise<void> {
    console.log('🔍 Checking service availability at:', this.serviceUrl);
    try {
      const response = await fetch(`${this.serviceUrl}/health`, {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      });

      if (response.ok) {
        this.isServiceAvailable = true;
        console.log('✅ Location sync service is available');
      } else {
        this.isServiceAvailable = false;
        console.log('⚠️ Location sync service is not responding, status:', response.status);
      }
    } catch (error) {
      this.isServiceAvailable = false;
      console.log('⚠️ Location sync service is not available, error:', error.message);
      console.log('📱 Using localStorage fallback');
    }
  }

  // Initialize WebSocket connection for real-time updates
  private initializeWebSocket(): void {
    if (!this.isServiceAvailable) {
      console.log('⚠️ Service not available, skipping WebSocket initialization');
      return;
    }

    if (this.ws) {
      console.log('⚠️ WebSocket already exists, closing existing connection');
      this.ws.close();
      this.ws = null;
    }

    try {
      console.log('🔄 Initializing WebSocket connection to:', this.wsUrl);
      this.ws = new WebSocket(this.wsUrl);

      this.ws.onopen = () => {
        console.log('✅ Connected to location sync service');
        this.reconnectAttempts = 0;
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('📨 WebSocket message received:', message);

          if (message.type === 'location-update') {
            const location = message.data.currentLocation || '';
            console.log('📡 Processing location update:', location);

            // Update localStorage for consistency
            if (location) {
              localStorage.setItem('userLocation', location);
            } else {
              localStorage.removeItem('userLocation');
            }

            // Notify all listeners
            console.log(`📢 Notifying ${this.listeners.length} listeners of location change`);
            this.notifyListeners(location);
          }
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = (event) => {
        console.log('🔌 WebSocket connection closed. Code:', event.code, 'Reason:', event.reason);
        this.ws = null;
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };
    } catch (error) {
      console.error('❌ Failed to initialize WebSocket:', error);
    }
  }

  // Attempt to reconnect WebSocket
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.log('❌ Max reconnection attempts reached');
      return;
    }

    this.reconnectAttempts++;
    console.log(`🔄 Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);

    setTimeout(() => {
      this.checkServiceAvailability().then(() => {
        if (this.isServiceAvailable) {
          this.initializeWebSocket();
        }
      });
    }, this.reconnectDelay);
  }

  // Get current location (service first, localStorage fallback)
  public async getCurrentLocation(): Promise<string> {
    // Ensure service is initialized first
    await this.ensureInitialized();

    // Try service first
    if (this.isServiceAvailable) {
      try {
        const response = await fetch(`${this.serviceUrl}/api/location`);
        if (response.ok) {
          const data: LocationResponse = await response.json();
          if (data.success) {
            console.log('📍 Got location from service:', data.location);

            // Update localStorage for consistency
            if (data.location) {
              localStorage.setItem('userLocation', data.location);
            }

            return data.location || '';
          }
        }
      } catch (error) {
        console.log('⚠️ Service unavailable, falling back to localStorage');
        this.isServiceAvailable = false;
      }
    }

    // Fallback to localStorage
    const localLocation = localStorage.getItem('userLocation') || '';
    console.log('📍 Got location from localStorage:', localLocation);
    return localLocation;
  }

  // Set location (admin only)
  public async setLocation(location: string, isAdmin: boolean, adminId?: string): Promise<boolean> {
    // Try service first
    if (this.isServiceAvailable) {
      try {
        const response = await fetch(`${this.serviceUrl}/api/location`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            location,
            isAdmin,
            adminId: adminId || 'admin'
          })
        });

        if (response.ok) {
          const data: LocationResponse = await response.json();
          if (data.success) {
            console.log('✅ Location updated via service:', location);
            return true;
          } else {
            console.error('❌ Service rejected location update:', data.error);
            return false;
          }
        }
      } catch (error) {
        console.log('⚠️ Service unavailable, falling back to localStorage');
        this.isServiceAvailable = false;
      }
    }

    // Fallback to localStorage (admin only)
    if (isAdmin) {
      if (location) {
        localStorage.setItem('userLocation', location);
      } else {
        localStorage.removeItem('userLocation');
      }
      console.log('📍 Location updated in localStorage:', location);
      
      // Notify listeners manually since no WebSocket
      this.notifyListeners(location);
      return true;
    }

    return false;
  }

  // Add listener for location changes
  public addLocationListener(callback: (location: string) => void): void {
    this.listeners.push(callback);
  }

  // Remove listener
  public removeLocationListener(callback: (location: string) => void): void {
    this.listeners = this.listeners.filter(listener => listener !== callback);
  }

  // Notify all listeners of location change
  private notifyListeners(location: string): void {
    console.log(`📢 Notifying ${this.listeners.length} listeners of location change to: "${location}"`);
    this.listeners.forEach((callback, index) => {
      try {
        callback(location);
        console.log(`✅ Listener ${index + 1} notified successfully`);
      } catch (error) {
        console.error(`❌ Error in location listener ${index + 1}:`, error);
      }
    });
  }

  // Check if service is available
  public isServiceOnline(): boolean {
    return this.isServiceAvailable;
  }

  // Check WebSocket connection status
  public isWebSocketConnected(): boolean {
    return this.ws !== null && this.ws.readyState === WebSocket.OPEN;
  }

  // Force WebSocket reconnection
  public forceReconnect(): void {
    console.log('🔄 Forcing WebSocket reconnection...');
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.reconnectAttempts = 0;
    this.checkServiceAvailability().then(() => {
      if (this.isServiceAvailable) {
        this.initializeWebSocket();
      }
    });
  }

  // Force refresh from service (useful for debugging and ensuring sync)
  public async forceRefresh(): Promise<string> {
    console.log('🔄 Force refreshing location from service...');

    // Ensure service is initialized first
    await this.ensureInitialized();

    // Re-check service availability
    await this.checkServiceAvailability();

    // Check WebSocket connection and re-initialize if needed
    if (this.isServiceAvailable) {
      if (!this.ws || this.ws.readyState !== WebSocket.OPEN) {
        console.log('🔄 WebSocket not connected, re-initializing...');
        this.initializeWebSocket();
      } else {
        console.log('✅ WebSocket connection is active');
      }
    }

    // Get fresh location data
    const location = await this.getCurrentLocation();

    // Notify all listeners
    this.notifyListeners(location);

    console.log(`🔄 Force refresh completed. Location: "${location}", WebSocket: ${this.isWebSocketConnected() ? 'Connected' : 'Disconnected'}`);

    return location;
  }

  // Clear localStorage location data (for debugging)
  public clearLocalStorage(): void {
    localStorage.removeItem('userLocation');
    console.log('🗑️ Cleared localStorage location data');
  }

  // Cleanup
  public disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
    this.listeners = [];
  }
}

// Export singleton instance
export const locationService = LocationSyncService.getInstance();

// Export types
export type { LocationData, LocationResponse };
