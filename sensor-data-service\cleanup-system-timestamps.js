#!/usr/bin/env node

/**
 * Cleanup System Timestamps
 * 
 * This script removes entries in Sensor_Data that use system-generated
 * timestamps (ISO format) instead of exact hall timestamps.
 */

const { getDatabase } = require('./firebase');

async function cleanupSystemTimestamps() {
  console.log('🧹 Cleaning up System Timestamps in Sensor_Data...\n');
  
  try {
    const db = getDatabase();
    const targetDate = '2025-07-20';
    const category = 'Basement';
    
    // Get Sensor_Data entries
    console.log('📊 Fetching Sensor_Data entries...');
    const sensorDataRef = db.ref(`Sensor_Data/${category}/${targetDate}`);
    const sensorDataSnapshot = await sensorDataRef.once('value');
    
    if (!sensorDataSnapshot.exists()) {
      console.log('❌ No Sensor_Data found');
      return;
    }
    
    const sensorData = sensorDataSnapshot.val();
    const allTimestamps = Object.keys(sensorData);
    
    console.log(`📊 Found ${allTimestamps.length} total entries`);
    
    // Identify entries with system timestamps (ISO format)
    const systemTimestampEntries = [];
    const hallTimestampEntries = [];
    
    allTimestamps.forEach(timeKey => {
      const data = sensorData[timeKey];
      if (data.timestamp && data.timestamp.includes('T') && data.timestamp.includes('Z')) {
        // System timestamp format: "2025-07-20T06:38:33.825Z"
        systemTimestampEntries.push(timeKey);
      } else if (data.timestamp && data.timestamp.includes(' ') && !data.timestamp.includes('T')) {
        // Hall timestamp format: "2025-07-20 12:08:12"
        hallTimestampEntries.push(timeKey);
      }
    });
    
    console.log(`✅ Hall timestamp entries: ${hallTimestampEntries.length}`);
    console.log(`❌ System timestamp entries: ${systemTimestampEntries.length}`);
    
    if (systemTimestampEntries.length === 0) {
      console.log('🎉 No system timestamp entries found! All entries are using exact hall timestamps.');
      return;
    }
    
    // Show the problematic entries
    console.log('\n❌ System timestamp entries to be removed:');
    systemTimestampEntries.forEach((timeKey, index) => {
      const data = sensorData[timeKey];
      console.log(`   ${index + 1}. ${timeKey} - timestamp: "${data.timestamp}"`);
    });
    
    // Remove system timestamp entries
    console.log(`\n🧹 Removing ${systemTimestampEntries.length} system timestamp entries...`);
    
    const updates = {};
    systemTimestampEntries.forEach(timeKey => {
      updates[`Sensor_Data/${category}/${targetDate}/${timeKey}`] = null;
    });
    
    await db.ref().update(updates);
    
    console.log('✅ System timestamp entries removed successfully!');
    
    // Verify the cleanup
    console.log('\n🔍 Verifying cleanup...');
    const verifySnapshot = await sensorDataRef.once('value');
    
    if (verifySnapshot.exists()) {
      const cleanedData = verifySnapshot.val();
      const remainingTimestamps = Object.keys(cleanedData);
      
      console.log(`📊 Remaining entries: ${remainingTimestamps.length}`);
      
      // Check if all remaining entries use hall timestamps
      const stillHaveSystemTimestamps = remainingTimestamps.some(timeKey => {
        const data = cleanedData[timeKey];
        return data.timestamp && data.timestamp.includes('T') && data.timestamp.includes('Z');
      });
      
      if (stillHaveSystemTimestamps) {
        console.log('⚠️ Some system timestamp entries still remain');
      } else {
        console.log('✅ All remaining entries use exact hall timestamps!');
      }
      
      // Show the cleaned pattern
      console.log('\n🕐 Latest 10 timestamps after cleanup:');
      const sortedTimestamps = remainingTimestamps.sort();
      sortedTimestamps.slice(-10).forEach((time, index) => {
        const data = cleanedData[time];
        console.log(`   ${index + 1}. ${time} - timestamp: "${data.timestamp}"`);
      });
      
      // Check for clustering
      const timeGroups = {};
      sortedTimestamps.forEach(time => {
        const hourMin = time.substring(0, 5);
        if (!timeGroups[hourMin]) {
          timeGroups[hourMin] = [];
        }
        timeGroups[hourMin].push(time);
      });
      
      const clusteredTimes = Object.entries(timeGroups).filter(([_, times]) => times.length > 1);
      
      if (clusteredTimes.length > 0) {
        console.log(`\n⚠️ Still found ${clusteredTimes.length} timestamp clusters:`);
        clusteredTimes.slice(0, 3).forEach(([hourMin, times]) => {
          console.log(`   ${hourMin}:XX - ${times.length} entries: ${times.join(', ')}`);
        });
      } else {
        console.log('\n✅ No timestamp clustering found - perfect 5-minute intervals!');
      }
      
    } else {
      console.log('❌ No data found after cleanup');
    }
    
    console.log('\n🎉 Cleanup completed!');
    
  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  }
}

// Handle script execution
if (require.main === module) {
  cleanupSystemTimestamps();
}

module.exports = { cleanupSystemTimestamps };
