{"name": "airsense-admin", "version": "0.1.0", "private": true, "dependencies": {"@types/react-router-dom": "^5.3.3", "cra-template-typescript": "1.3.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "react-scripts": "5.0.1"}, "scripts": {"start": "set PORT=3004 && set BROWSER=none && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}