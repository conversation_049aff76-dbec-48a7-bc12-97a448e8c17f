#!/usr/bin/env node

/**
 * Initialize Sensor_Data Section in Firebase
 * 
 * This script creates a new "Sensor_Data" section in Firebase with three categories:
 * - Lecture Hall
 * - Basement  
 * - Dean's Office
 * 
 * Usage: node initialize-sensor-data-section.js
 */

const { FirebaseService, initializeFirebase } = require('./firebase');

async function initializeSensorDataSection() {
  console.log('🚀 Starting Sensor_Data section initialization...');
  
  try {
    // Initialize Firebase connection
    console.log('🔥 Connecting to Firebase...');
    initializeFirebase();
    
    // Initialize the Sensor_Data section with categories
    console.log('📁 Creating Sensor_Data section with categories...');
    await FirebaseService.initializeSensorDataSection();
    
    // Verify the categories were created
    console.log('🔍 Verifying categories...');
    const categories = await FirebaseService.getSensorDataCategories();
    console.log('✅ Created categories:', categories);
    
    // Add sample data to demonstrate the structure (optional)
    console.log('📝 Adding sample data to demonstrate structure...');
    const sampleData = {
      temperature: 22.5,
      humidity: 45.2,
      co2: 850,
      pm25: 15.3,
      pm10: 22.1,
      aqi: 45,
      gasresistance: 250.5,
      original_timestamp: new Date().toISOString()
    };
    
    const timestamp = new Date().toISOString();
    
    // Add sample data to each category
    for (const category of categories) {
      await FirebaseService.writeSensorDataToCategory(category, timestamp, {
        ...sampleData,
        location: category,
        note: 'Sample initialization data'
      });
      console.log(`📊 Added sample data to ${category}`);
    }
    
    console.log('\n🎉 Sensor_Data section initialization completed successfully!');
    console.log('\n📋 Database Structure Created:');
    console.log('Sensor_Data/');
    console.log('├── Lecture Hall/');
    console.log('│   ├── _metadata/');
    console.log('│   ├── [timestamp]/     # Sensor readings');
    console.log('│   └── dailyAverage/    # Daily averages (future)');
    console.log('├── Basement/');
    console.log('│   ├── _metadata/');
    console.log('│   ├── [timestamp]/     # Sensor readings');
    console.log('│   └── dailyAverage/    # Daily averages (future)');
    console.log("└── Dean's Office/");
    console.log('    ├── _metadata/');
    console.log('    ├── [timestamp]/     # Sensor readings');
    console.log('    └── dailyAverage/    # Daily averages (future)');
    
    console.log('\n🔗 Firebase Database URL: https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/');
    console.log('📍 Navigate to: Sensor_Data/ to view the new section');
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error initializing Sensor_Data section:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  initializeSensorDataSection();
}

module.exports = { initializeSensorDataSection };
