#!/usr/bin/env node

/**
 * Location Synchronization Diagnostic Tool
 * 
 * This script helps diagnose why "Location requested" messages might not be appearing
 * and checks the overall health of the location synchronization system.
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

const LOCATION_SERVICE_URL = 'http://localhost:3002';
const DATA_FILE = path.join(__dirname, 'location-service', 'location-data.json');

async function runDiagnostics() {
  console.log('🔍 Location Synchronization Diagnostics');
  console.log('=====================================\n');

  const results = [];

  // Check 1: Location service availability
  console.log('1. Checking location service availability...');
  try {
    const healthData = await makeRequest('/health');
    if (healthData.status === 'healthy') {
      console.log('   ✅ Location service is running and healthy');
      console.log(`   📊 Connected clients: ${healthData.connectedClients}`);
      results.push({ check: 'Service Health', status: 'PASS' });
    } else {
      throw new Error('Service not healthy');
    }
  } catch (error) {
    console.log('   ❌ Location service is not available');
    console.log(`   🔍 Error: ${error.message}`);
    results.push({ check: 'Service Health', status: 'FAIL', error: error.message });
  }

  // Check 2: Location data file
  console.log('\n2. Checking location data file...');
  try {
    if (fs.existsSync(DATA_FILE)) {
      const data = JSON.parse(fs.readFileSync(DATA_FILE, 'utf8'));
      console.log('   ✅ Location data file exists');
      console.log(`   📍 Current location: "${data.currentLocation || 'none'}"`);
      console.log(`   ⏰ Last updated: ${data.lastUpdated || 'never'}`);
      console.log(`   👤 Updated by: ${data.updatedBy || 'unknown'}`);
      results.push({ check: 'Data File', status: 'PASS' });
    } else {
      throw new Error('Location data file not found');
    }
  } catch (error) {
    console.log('   ❌ Location data file issue');
    console.log(`   🔍 Error: ${error.message}`);
    results.push({ check: 'Data File', status: 'FAIL', error: error.message });
  }

  // Check 3: API endpoints
  console.log('\n3. Testing API endpoints...');
  try {
    console.log('   📡 Testing GET /api/location...');
    const locationData = await makeRequest('/api/location');
    if (locationData.success) {
      console.log('   ✅ GET /api/location works');
      console.log(`   📍 Returned location: "${locationData.location || 'none'}"`);
      results.push({ check: 'GET Location API', status: 'PASS' });
    } else {
      throw new Error('API returned success: false');
    }
  } catch (error) {
    console.log('   ❌ GET /api/location failed');
    console.log(`   🔍 Error: ${error.message}`);
    results.push({ check: 'GET Location API', status: 'FAIL', error: error.message });
  }

  // Check 4: Test location update
  console.log('\n4. Testing location update...');
  try {
    console.log('   📡 Testing POST /api/location...');
    const updateData = await makeRequest('/api/location', 'POST', {
      location: 'lecture-hall',
      isAdmin: true,
      adminId: 'diagnostic-test'
    });
    if (updateData.success) {
      console.log('   ✅ POST /api/location works');
      console.log(`   📍 Updated to: "${updateData.location}"`);
      results.push({ check: 'POST Location API', status: 'PASS' });
    } else {
      throw new Error('API returned success: false');
    }
  } catch (error) {
    console.log('   ❌ POST /api/location failed');
    console.log(`   🔍 Error: ${error.message}`);
    results.push({ check: 'POST Location API', status: 'FAIL', error: error.message });
  }

  // Check 5: Verify update persistence
  console.log('\n5. Verifying location update persistence...');
  try {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    const verifyData = await makeRequest('/api/location');
    if (verifyData.success && verifyData.location === 'lecture-hall') {
      console.log('   ✅ Location update persisted correctly');
      results.push({ check: 'Update Persistence', status: 'PASS' });
    } else {
      throw new Error(`Expected 'lecture-hall', got '${verifyData.location}'`);
    }
  } catch (error) {
    console.log('   ❌ Location update did not persist');
    console.log(`   🔍 Error: ${error.message}`);
    results.push({ check: 'Update Persistence', status: 'FAIL', error: error.message });
  }

  // Summary
  console.log('\n📊 Diagnostic Summary:');
  console.log('=====================');
  
  let passed = 0;
  let failed = 0;

  results.forEach(result => {
    const status = result.status === 'PASS' ? '✅' : '❌';
    console.log(`${status} ${result.check}: ${result.status}`);
    if (result.error) {
      console.log(`   Error: ${result.error}`);
    }
    
    if (result.status === 'PASS') passed++;
    else failed++;
  });

  console.log(`\nTotal: ${results.length} checks`);
  console.log(`Passed: ${passed}`);
  console.log(`Failed: ${failed}`);

  // Recommendations
  console.log('\n💡 Recommendations:');
  if (failed === 0) {
    console.log('✅ All checks passed! The location service is working correctly.');
    console.log('📋 If you\'re not seeing "Location requested" messages:');
    console.log('   1. Make sure you\'re looking at the correct terminal (labeled "SYNC")');
    console.log('   2. Try refreshing your browser or opening a new tab');
    console.log('   3. Check browser console for any JavaScript errors');
    console.log('   4. Run: node test-location-requests.js to generate test requests');
  } else {
    console.log('⚠️ Some checks failed. Please address the issues above.');
    console.log('🔧 Common solutions:');
    console.log('   1. Restart the location service: cd location-service && npm run dev');
    console.log('   2. Check if port 3002 is available');
    console.log('   3. Verify all dependencies are installed: npm install');
  }
}

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      hostname: 'localhost',
      port: 3002,
      path: path,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = http.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const parsedData = JSON.parse(responseData);
          resolve(parsedData);
        } catch (error) {
          reject(new Error(`Invalid JSON response: ${responseData}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    if (data) {
      req.write(JSON.stringify(data));
    }

    req.end();
  });
}

// Run diagnostics if this script is executed directly
if (require.main === module) {
  runDiagnostics().catch(error => {
    console.error('❌ Diagnostic execution failed:', error);
    process.exit(1);
  });
}

module.exports = { runDiagnostics };
