# AirSense Sensor Data Service

Firebase-powered backend service for the AirSense air quality monitoring system with **location-aware data collection** and **admin session management**. This service handles sensor data ingestion from ESP32 devices, stores data in Firebase Realtime Database based on admin-selected locations, and provides comprehensive admin control over data collection.

## 🚀 Features

### Core Features
- **Real-time sensor data ingestion** from ESP32 devices
- **Firebase Realtime Database** integration for data storage
- **WebSocket support** for real-time data streaming
- **Daily average calculations** with automated scheduling
- **Data validation** and **AQI calculation**

### New Location-Aware Features ✨
- **Admin Authentication System**: Secure login with `<EMAIL>` / `admin123`
- **Location-Based Data Collection**: Data is stored only with admin-selected locations
- **5-Minute Delayed Storage**: Sensor data is stored in database with a 5-minute delay
- **Admin Session Management**: Data collection starts/stops based on admin login/logout
- **Device Restart Persistence**: Uses last admin-selected location when device restarts
- **Automatic Data Collection Control**: No data storage when admin is logged out
- **Location History Tracking**: Complete audit trail of location changes
- **Delayed Storage Queue Management**: Monitor and manage pending sensor data

- **ESP32 Integration**: Receives hourly sensor data via POST endpoints
- **Firebase Storage**: Stores data in Firebase Realtime Database with real-time sync
- **Daily Averages**: Automatically calculates and stores daily averages
- **Real-time Updates**: WebSocket support for live data streaming
- **Data Validation**: Comprehensive validation of sensor readings
- **AQI Calculation**: Automatic Air Quality Index calculation
- **Rate Limiting**: Protection against API abuse
- **Security**: Helmet.js security headers

## 📊 Enhanced Database Structure

```
sensorData/
├── deans-office/
│   ├── 2024-01-15T10:30:00.000Z/     # Sensor readings with location metadata
│   ├── 2024-01-15T11:30:00.000Z/
│   └── dailyAverage/
│       ├── 2024-01-15/               # Daily averages
│       └── 2024-01-16/
├── lecture-hall/
├── basement/
adminSession/                          # Admin login/logout tracking
├── isLoggedIn: true/false
├── adminEmail: "<EMAIL>"
├── timestamp: ServerValue.TIMESTAMP
└── lastActivity: "2024-01-15T10:30:00.000Z"
activeLocation/                        # Current admin-selected location
├── location: "deans-office"
├── setBy: "<EMAIL>"
├── timestamp: ServerValue.TIMESTAMP
└── setAt: "2024-01-15T10:30:00.000Z"
dataCollectionStatus/                  # Data collection control
├── isActive: true/false
├── location: "deans-office"
├── controlledBy: "<EMAIL>"
├── timestamp: ServerValue.TIMESTAMP
└── lastUpdated: "2024-01-15T10:30:00.000Z"
locationHistory/                       # Audit trail of location changes
├── {pushId}/
│   ├── location: "deans-office"
│   ├── setBy: "<EMAIL>"
│   ├── action: "location_set"
│   └── timestamp: ServerValue.TIMESTAMP
└── {pushId}/
    ├── location: null
    ├── setBy: "<EMAIL>"
    ├── action: "location_cleared"
    └── timestamp: ServerValue.TIMESTAMP
```

## 🛠️ Setup

### Prerequisites
- Node.js 16+
- Firebase project with Realtime Database
- Firebase Admin SDK service account key

### Installation

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure Firebase**:
   - Place your `serviceAccountKey.json` in the service directory
   - Update `firebase.js` with your database URL if needed

3. **Start the service**:
   ```bash
   # Development
   npm run dev
   
   # Production
   npm start
   ```

## 📡 API Endpoints

### Health Check
```http
GET /health
```

### Sensor Data Ingestion (ESP32) - Location-Aware ✨
```http
POST /api/sensor-data
Content-Type: application/json

{
  "temperature": 22.5,
  "humidity": 45.2,
  "co2": 850,
  "pm25": 15.3,
  "pm10": 22.1,
  "voc": 250,
  "deviceLocation": "deans-office"  // Optional: device's last known location
}
```

**Response when data collection is active (5-minute delayed storage):**
```json
{
  "success": true,
  "location": "deans-office",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "aqi": 45,
  "deviceId": "esp32-main",
  "delayedStorage": true,
  "willStoreAt": "2024-01-15T10:35:00.000Z",
  "delayMinutes": 5
}
```

**Response when admin is not logged in:**
```json
{
  "success": false,
  "message": "Data collection not active - Admin not logged in or no location selected",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "dataCollectionActive": false
}
```

### Legacy Sensor Data Endpoint (Backward Compatibility)
```http
POST /api/sensor-data/:location
Content-Type: application/json

{
  "temperature": 22.5,
  "humidity": 45.2,
  "co2": 850,
  "pm25": 15.3,
  "pm10": 22.1,
  "voc": 250
}
```

### Get Latest Reading
```http
GET /api/sensor-data/:location/latest
```

### Get Historical Data
```http
GET /api/sensor-data/:location/history?limit=100
```

### Get Daily Averages
```http
GET /api/sensor-data/:location/daily-averages?days=30
```

## 👑 Admin Management Endpoints ✨

### Admin Login
```http
POST /api/admin/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "admin123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Admin logged in successfully",
  "adminEmail": "<EMAIL>",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Admin Logout
```http
POST /api/admin/logout
Content-Type: application/json

{
  "email": "<EMAIL>"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Admin logged out successfully",
  "adminEmail": "<EMAIL>",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Location Selection (Admin Only)
```http
POST /api/admin/location
Content-Type: application/json

{
  "location": "deans-office",
  "adminEmail": "<EMAIL>"
}
```

**Valid locations:** `deans-office`, `lecture-hall`, `basement`

**Response:**
```json
{
  "success": true,
  "message": "Location selected successfully",
  "location": "deans-office",
  "adminEmail": "<EMAIL>",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

### Get Admin Status
```http
GET /api/admin/status
```

**Response:**
```json
{
  "success": true,
  "status": {
    "controller": {
      "lastKnownLocation": "deans-office",
      "dataCollectionActive": true,
      "deviceId": "esp32-main"
    },
    "session": {
      "adminSession": {
        "isLoggedIn": true,
        "adminEmail": "<EMAIL>",
        "timestamp": 1642248600000
      },
      "activeLocation": {
        "location": "deans-office",
        "setBy": "<EMAIL>",
        "timestamp": 1642248600000
      },
      "dataCollection": {
        "isActive": true,
        "location": "deans-office",
        "controlledBy": "<EMAIL>"
      }
    }
  }
}
```

### Device Restart Handler
```http
POST /api/device/restart
```

**Response:**
```json
{
  "success": true,
  "message": "Device restart handled",
  "location": "deans-office",
  "dataCollectionActive": true
}
```

## ⏰ Delayed Storage Management Endpoints ✨

### Get Delayed Storage Queue Status
```http
GET /api/delayed-storage/status
```

**Response:**
```json
{
  "success": true,
  "status": {
    "total": 15,
    "ready": 3,
    "pending": 12,
    "failed": 0,
    "delayMinutes": 5,
    "isRunning": true
  }
}
```

### Get Pending Delayed Storage Data (Debug)
```http
GET /api/delayed-storage/pending
```

**Response:**
```json
{
  "success": true,
  "pendingData": [
    {
      "id": "esp32-main_1642248600000",
      "location": "deans-office",
      "deviceId": "esp32-main",
      "receivedAt": "2024-01-15T10:30:00.000Z",
      "storeAt": "2024-01-15T10:35:00.000Z",
      "status": "queued"
    }
  ],
  "count": 1
}
```

### Clear Delayed Storage Queue (Admin Only)
```http
POST /api/delayed-storage/clear
```

**Response:**
```json
{
  "success": true,
  "message": "Cleared 15 items from delayed storage queue",
  "clearedCount": 15
}
```

### Manual Daily Average Calculation
```http
POST /api/daily-averages/calculate
Content-Type: application/json

{
  "date": "2024-01-15",
  "location": "deans-office"  // optional
}
```

### Daily Average Status
```http
GET /api/daily-averages/status
```

## 🔌 WebSocket Events

Connect to `ws://localhost:3003` to receive real-time updates:

```javascript
{
  "type": "sensor-data-update",
  "location": "deans-office",
  "data": {
    "temperature": 22.5,
    "humidity": 45.2,
    "co2": 850,
    "pm25": 15.3,
    "aqi": 65,
    "timestamp": "2024-01-15T10:30:00.000Z"
  }
}
```

## 🧪 Testing

Run the comprehensive test suite:

```bash
# Start the server first
npm run dev

# In another terminal, run tests
npm test
```

The test suite includes:
- Health check verification
- ESP32 data simulation
- Data ingestion testing
- Retrieval endpoint testing
- Daily average calculation
- WebSocket functionality

## 📊 Daily Averages

The service automatically calculates daily averages at 00:30 each day. Daily averages include:

- **Average values**: temperature, humidity, CO2, PM2.5, PM10, VOC, AQI
- **Min/Max values**: For trend analysis
- **Median values**: For better statistical representation
- **Metadata**: Data point count, calculation time, first/last readings

## 🔧 Configuration

### Environment Variables
- `PORT`: Service port (default: 3003)
- `NODE_ENV`: Environment (development/production)

### Firebase Configuration
Update `firebase.js` to modify:
- Database URL
- Service account path
- Database structure paths

## 🚨 Error Handling

The service includes comprehensive error handling:
- Input validation with detailed error messages
- Firebase connection error recovery
- Rate limiting protection
- Graceful shutdown handling

## 📈 Monitoring

Monitor service health via:
- `/health` endpoint for status checks
- Console logging with emoji indicators
- WebSocket connection count tracking
- Daily average calculation status

## 🔒 Security Features

- **Helmet.js**: Security headers
- **Rate Limiting**: 100 requests per 15 minutes per IP
- **Input Validation**: Comprehensive sensor data validation
- **CORS**: Configured for specific origins
- **Error Sanitization**: No sensitive data in error responses

## 🏗️ Architecture

```
ESP32 Sensors → POST /api/sensor-data/:location → Firebase Realtime DB
                                                      ↓
Frontend Apps ← WebSocket Updates ← Real-time Sync ←┘
                ↑
            REST API Endpoints
```

## 📝 Logs

The service provides detailed logging:
- 🚀 Service startup
- 📊 Sensor data ingestion
- 📈 Daily average calculations
- 🔌 WebSocket connections
- ❌ Error conditions
- 📡 Data broadcasting

## 🔄 Integration

This service integrates with:
- **ESP32 devices**: For sensor data collection
- **React frontend**: For data visualization
- **Admin interface**: For system management
- **Location service**: For multi-machine sync

## 📋 Supported Locations

- `deans-office`
- `lecture-hall`
- `basement`

Add new locations by updating the validation arrays in `server.js`.

## 🎯 Usage Scenarios

### Scenario 1: Admin Login and Location Selection
1. Admin opens the admin interface (`http://localhost:3004`)
2. Logs in with `<EMAIL>` / `admin123`
3. Selects a location (e.g., "Dean's Office")
4. Data collection starts automatically
5. ESP32 devices send data to the selected location
6. **Data is queued for 5-minute delayed storage**
7. After 5 minutes, data is automatically stored in Firebase

### Scenario 2: Device Restart with Active Admin Session
1. ESP32 device restarts while admin is logged in
2. Device calls `/api/device/restart` on startup
3. Service returns the last admin-selected location
4. Device continues sending data to that location
5. **Data continues to be queued with 5-minute delay**

### Scenario 3: Admin Logout
1. Admin clicks home icon or logs out
2. Admin interface calls `/api/admin/logout`
3. Data collection stops immediately
4. **Queued data continues to be stored (already in 5-minute delay queue)**
5. New ESP32 data is rejected until admin logs in again

### Scenario 4: Device Restart with No Active Admin
1. ESP32 device restarts when admin is logged out
2. Device calls `/api/device/restart` on startup
3. Service returns no active location
4. Device data is rejected until admin logs in and selects location

## 🧪 Testing the Database Integration

### Run Comprehensive Tests
```bash
# Start the sensor data service
cd sensor-data-service
npm run dev

# In another terminal, run the integration tests
node test-database-integration.js
```

### Run ESP32 Simulation
```bash
# Simulate ESP32 device behavior
node esp32-integration-example.js
```

### Manual Testing Steps
1. **Test Admin Login**: Use admin interface or call API directly
2. **Test Location Selection**: Select different locations via admin interface
3. **Test Data Collection**: Send sensor data and verify it's stored with correct location
4. **Test Admin Logout**: Logout and verify data collection stops
5. **Test Device Restart**: Restart simulation and verify location persistence

### Expected Test Results
- ✅ Admin login/logout functionality
- ✅ Location-based data storage
- ✅ Data collection control based on admin session
- ✅ Device restart location persistence
- ✅ Data rejection when admin is logged out

## 🔍 Monitoring and Debugging

### Check Admin Session Status
```bash
curl http://localhost:3003/api/admin/status
```

### Check Delayed Storage Queue Status
```bash
curl http://localhost:3003/api/delayed-storage/status
```

### Monitor Pending Delayed Storage Data
```bash
curl http://localhost:3003/api/delayed-storage/pending
```

### Monitor Firebase Database
- Open Firebase Console
- Navigate to Realtime Database
- Check `adminSession`, `activeLocation`, `dataCollectionStatus` nodes

### View Service Logs
The service provides detailed console logging:
- 👑 Admin session events
- 📍 Location changes
- 📊 Data collection status
- ⏰ Delayed storage queue operations
- 📦 Data processing and storage events
- 📱 Device restart events
- ❌ Error conditions

## 🚀 Production Deployment

### Environment Setup
1. Set production Firebase credentials
2. Configure environment variables
3. Set up process monitoring (PM2 recommended)
4. Configure reverse proxy (nginx recommended)

### Security Considerations
- Change default admin credentials
- Implement proper authentication tokens
- Set up HTTPS for production
- Configure firewall rules
- Monitor for suspicious activity

## 📞 Support

For issues or questions about the database integration:
1. Check the console logs for detailed error messages
2. Verify Firebase connection and credentials
3. Test admin authentication endpoints
4. Run the integration test suite
5. Check the admin interface functionality
