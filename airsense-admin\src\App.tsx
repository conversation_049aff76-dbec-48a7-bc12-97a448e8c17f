import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Login from './components/Login.tsx';
import LocationSelection from './components/LocationSelection.tsx';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<Login />} />
          <Route path="/location-selection" element={<LocationSelection />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
