import React, { useState, useEffect } from 'react';
import Layout from '../layouts/Layout';
import CircularGauge from '../components/CircularGauge';
import LineChart from '../components/LineChart';
import { sensorDataService } from '../services/sensorDataService';
import { locationManager } from '../services/locationManager';
import '../styles/Dashboard.css';

// Simple sensor data hook - NO POLLING to prevent excessive API calls
const useSensorData = (location: string) => {
  const [data, setData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');

  const fetchData = async () => {
    if (!location) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(''); // Clear previous errors
      console.log('🔄 useSensorData: Fetching data for location:', location);

      const sensorData = await sensorDataService.getLatestSensorData(location);
      console.log('✅ useSensorData: Data received for', location, ':', sensorData);

      setData(sensorData);
      setError('');
    } catch (err: any) {
      console.error('❌ useSensorData: Error fetching data for', location, ':', err);
      setError(err.message || 'Failed to fetch sensor data');
      setData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [location]);

  return { data, loading, error, refetch: fetchData };
};

// Air quality standards
const baseAirQualityStandards = {
  Temperature: { 
    poor: { min: -Infinity, max: 18, unit: '°C' },
    moderate: { min: 18, max: 21.8, unit: '°C' },
    good: { min: 21.8, max: 26.10, unit: '°C' },
    moderate2: { min: 26.1, max: 30, unit: '°C' },
    poor2: { min: 30, max: Infinity, unit: '°C' }
  },
  Humidity: {
    poor: { min: -Infinity, max: 20, unit: '%' },
    moderate: { min: 20, max: 30, unit: '%' },
    good: { min: 30, max: 60, unit: '%' },
    moderate2: { min: 60, max: 70, unit: '%' },
    poor2: { min: 70, max: Infinity, unit: '%' }
  },
  'CO2 Level': {
    good: { min: 400, max: 800, unit: 'ppm' },
    moderate: { min: 800, max: 1200, unit: 'ppm' },
    poor: { min: 1200, max: Infinity, unit: 'ppm' }
  },
  'PM2.5': {
    good: { min: 0, max: 100, unit: 'µg/m³' },
    moderate: { min: 100, max: 125, unit: 'µg/m³' },
    poor: { min: 125, max: Infinity, unit: 'µg/m³' }
  },
  'PM10': {
    good: { min: 0, max: 200, unit: 'µg/m³' },
    moderate: { min: 200, max: 250, unit: 'µg/m³' },
    poor: { min: 250, max: Infinity, unit: 'µg/m³' }
  },
  'Gas Resistance': {
    good: { min: 50, max: Infinity, unit: 'kΩ' },
    moderate: { min: 10, max: 50, unit: 'kΩ' },
    poor: { min: 0, max: 10, unit:'kΩ' }
  },
  'Pressure': {
    good: { min: 980, max: 1020, unit: 'hPa' },
    moderate: { min: 960, max: 980, unit: 'hPa' },
    poor: { min: -Infinity, max: 960, unit: 'hPa' }
  },
  'Nitrogen Dioxide (NO2)': {
    good: { min: 0, max: 110, unit: 'ppb' },
    moderate: { min: 110, max: 130, unit: 'ppb' },
    poor: { min: 130, max: Infinity, unit: 'ppb' }
  },
  'Ozone (O3)': {
    good: { min: 0, max: 100, unit: 'ppb' },
    moderate: { min: 100, max: 120, unit: 'ppb' },
    poor: { min: 120, max: Infinity, unit: 'ppb' }
  }
};

// Calculate AQI based on multiple metrics (kept as fallback - primary AQI now comes from Firebase)
const calculateAQI = (metrics: Record<string, number>) => {
  const breakpoints = {
    pm25: [
      { low: 0, high: 12, aqiLow: 0, aqiHigh: 50 },
      { low: 12.1, high: 35.4, aqiLow: 51, aqiHigh: 100 },
      { low: 35.5, high: 55.4, aqiLow: 101, aqiHigh: 150 },
      { low: 55.5, high: 150.4, aqiLow: 151, aqiHigh: 200 },
      { low: 150.5, high: 250.4, aqiLow: 201, aqiHigh: 300 },
      { low: 250.5, high: 500.4, aqiLow: 301, aqiHigh: 500 }
    ]
  };

  const pm25Value = metrics['PM2.5'] || 0;
  let aqi = 0;

  for (const bp of breakpoints.pm25) {
    if (pm25Value >= bp.low && pm25Value <= bp.high) {
      aqi = Math.round(((bp.aqiHigh - bp.aqiLow) / (bp.high - bp.low)) * (pm25Value - bp.low) + bp.aqiLow);
      break;
    }
  }

  return Math.min(500, Math.max(0, aqi));
};

/*
// Alternative: Commented out version if you want to disable local calculation entirely
const calculateAQI = (metrics: Record<string, number>) => {
  // AQI calculation disabled - using Firebase stored AQI values
  return 0;
};
*/

// This function has been removed - dashboard now always displays latest data from Firebase

// Get human-readable data age
const getDataAge = (timestamp: string) => {
  if (!timestamp) return 'Unknown';

  const cleanTimestamp = timestamp.replace(/_/g, ':').replace(/(\d{3})Z$/, '.$1Z');
  const dataTime = new Date(cleanTimestamp);
  const now = new Date();
  const diffMs = now.getTime() - dataTime.getTime();

  const diffMinutes = Math.floor(diffMs / (1000 * 60));
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffMinutes < 60) {
    return `${diffMinutes} minutes ago`;
  } else if (diffHours < 24) {
    return `${diffHours} hours ago`;
  } else {
    return `${diffDays} days ago`;
  }
};

// Parse Firebase timestamp key to readable format (when data was actually stored)
const parseFirebaseTimestamp = (timestamp: string) => {
  if (!timestamp) return 'No timestamp';

  console.log('🔍 Parsing Firebase timestamp key:', timestamp);

  try {
    // Firebase timestamp format: 2025-07-14T19_44_29_123Z
    // Convert to: 2025-07-14T19:44:29.123Z
    let cleanTimestamp = timestamp;

    if (timestamp.includes('_')) {
      // Replace underscores with colons for time part
      cleanTimestamp = timestamp.replace(/_/g, ':');
      // Fix milliseconds format: replace last :123Z with .123Z
      cleanTimestamp = cleanTimestamp.replace(/:(\d{3})Z$/, '.$1Z');
    }

    console.log('🔍 Cleaned timestamp:', cleanTimestamp);

    const date = new Date(cleanTimestamp);

    if (!isNaN(date.getTime())) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      const formatted = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      console.log('🔍 Final formatted timestamp:', formatted);
      return formatted;
    }
  } catch (error) {
    console.warn('❌ Error parsing Firebase timestamp:', error);
  }

  return 'Invalid timestamp';
};

// Convert Firebase sensor data to Dashboard format
const convertSensorDataToDashboard = (sensorData: any) => {
  if (!sensorData) return { data: {}, age: 'No data', hasData: false };

  // Using current system time instead of Firebase timestamp for accuracy

  // Get data age for display purposes only
  const age = getDataAge(sensorData.timestamp);

  // Always show data if it exists from Firebase
  const data = {
    Temperature: parseFloat(sensorData.temperature?.toFixed(1)) || 0,
    Humidity: parseFloat(sensorData.humidity?.toFixed(1)) || 0,
    'PM2.5': parseFloat(sensorData.pm25?.toFixed(1)) || 0,
    'PM10': parseFloat(sensorData.pm10?.toFixed(1)) || 0,
    'Nitrogen Dioxide (NO2)': Math.round(sensorData.no2) || 0,
    'Ozone (O3)': Math.round(sensorData.o3) || 0,
    'CO2 Level': Math.round(sensorData.co2) || 0,
    'Pressure': parseFloat(sensorData.pressure?.toFixed(1)) || 0,  // Fixed: round to 1 decimal place
    'Gas Resistance': parseFloat(sensorData.gasResistance?.toFixed(1)) || 0  // Fixed: round to 1 decimal place
  };

  // Use AQI from Firebase if available, otherwise calculate it
  const aqi = sensorData.aqi || calculateAQI(data);

  // Parse the Firebase timestamp key (when data was actually stored)
  const storedTimestamp = parseFirebaseTimestamp(sensorData.timestamp);
  console.log('🔍 Data stored timestamp:', storedTimestamp);

  return {
    data: { ...data, AQI: aqi },
    age: age,
    timestamp: storedTimestamp,
    formattedTimestamp: storedTimestamp,
    hasData: true
  };
};

const getQualityStatus = (metric: string, value: number) => {
  if (metric === 'AQI') {
    if (value <= 66) return 'good';
    if (value <= 99) return 'moderate';
    return 'poor';
  }

  const standards = baseAirQualityStandards[metric as keyof typeof baseAirQualityStandards];
  if (!standards) return 'moderate';

  // Handle Temperature and Humidity with multiple ranges
  if (metric === 'Temperature' || metric === 'Humidity') {
    if (value >= standards.good.min && value <= standards.good.max) return 'good';
    if ((value >= standards.moderate.min && value <= standards.moderate.max) ||
        ((standards as any).moderate2 && value >= (standards as any).moderate2.min && value <= (standards as any).moderate2.max)) {
      return 'moderate';
    }
    return 'poor';
  }

  // Handle other metrics
  for (const [status, range] of Object.entries(standards)) {
    if (status.includes('2')) continue; // Skip duplicate ranges
    if (value >= range.min && value <= range.max) {
      return status;
    }
  }
  return 'moderate';
};





// Fetch historical chart data from Firebase using real-time data location
const fetchHistoricalChartData = async (metric: string, dateFrom: string, dateTo: string, realTimeLocation: string) => {
  try {
    // Use the same location as real-time data display
    const location = realTimeLocation;
    console.log('🔍 Using real-time data location for historical data:', location);

    // Convert metric name to Firebase field name
    const metricFieldMap: { [key: string]: string } = {
      'Temperature': 'temperature',
      'Humidity': 'humidity',
      'PM2.5': 'pm25',
      'PM10': 'pm10',
      'CO2 Level': 'co2',
      'Gas Resistance': 'gasResistance',  // Fixed: use gasResistance instead of voc
      'Pressure': 'pressure',
      'Nitrogen Dioxide (NO2)': 'no2',
      'Ozone (O3)': 'o3',
      'AQI': 'aqi'
    };

    const fieldName = metricFieldMap[metric];
    if (!fieldName) {
      throw new Error(`Metric "${metric}" is not supported. Please select another metric.`);
    }

    // Get historical data using existing API and calculate daily averages
    const startDate = new Date(dateFrom);
    const endDate = new Date(dateTo);
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    console.log('🔍 Fetching historical data for date range:', dateFrom, 'to', dateTo);
    console.log('🔍 Location:', location, 'Metric:', metric, 'Field:', fieldName);

    // Fetch ALL historical data for the location (no limit to get all data since 2025-07-09)
    const apiUrl = `http://localhost:3003/api/sensor-data/${location}/history?limit=10000`;
    console.log('🔍 Fetching ALL historical data from URL:', apiUrl);
    console.log('🔍 Looking for data from 2025-07-09 onwards');

    const response = await fetch(apiUrl);

    if (!response.ok) {
      console.error('🔍 API response not ok:', response.status, response.statusText);
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    console.log('🔍 API response received');

    if (!result.success || !result.data) {
      console.log('🔍 No historical data found in response');
      throw new Error('No historical data available');
    }

    console.log('🔍 Total historical records found:', result.data.length);

    // Log date range of available data
    if (result.data.length > 0) {
      const timestamps = result.data.map((d: any) => d.timestamp).filter(Boolean).sort();
      if (timestamps.length > 0) {
        console.log('🔍 Data available from:', timestamps[0], 'to:', timestamps[timestamps.length - 1]);
      }
    }

    // Process the data for chart - calculate daily averages from historical data
    const labels: string[] = [];
    const data: number[] = [];

    for (let i = 0; i < days; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateStr = date.toISOString().split('T')[0]; // YYYY-MM-DD format

      labels.push(date.toLocaleDateString());

      // Filter data for this specific date from historical records
      const dayReadings = result.data.filter((reading: any) => {
        if (reading.timestamp && reading.timestamp.startsWith(dateStr)) {
          return true;
        }
        return false;
      });

      console.log(`🔍 ${dateStr}: Found ${dayReadings.length} total readings for this date`);

      if (dayReadings.length > 0) {
        // Show sample timestamps for debugging
        const sampleTimestamps = dayReadings.slice(0, 3).map((r: any) => r.timestamp);
        console.log(`🔍 ${dateStr}: Sample timestamps:`, sampleTimestamps);

        // Calculate daily average from readings for this date
        const validValues = dayReadings
          .map((reading: any) => reading[fieldName])
          .filter((value: any) => value !== undefined && value !== null && !isNaN(value));

        console.log(`🔍 ${dateStr}: Valid ${fieldName} values found: ${validValues.length}`);

        if (validValues.length > 0) {
          const average = validValues.reduce((sum: number, val: number) => sum + val, 0) / validValues.length;
          data.push(parseFloat(average.toFixed(2)));
          console.log(`🔍 ${dateStr}: Calculated average for ${fieldName} = ${average.toFixed(2)}`);
        } else {
          data.push(0);
          console.log(`🔍 ${dateStr}: No valid ${fieldName} readings found, using 0`);
        }
      } else {
        data.push(0);
        console.log(`🔍 ${dateStr}: No data found for this date, using 0`);
      }
    }

    // Get color for this metric
    const getMetricColor = (metric: string) => {
      const colorMap: { [key: string]: string } = {
        'AQI': '#EA4335',
        'Temperature': '#34A853',
        'Humidity': '#FBBC05',
        'PM2.5': '#4285F4',
        'PM10': '#9C27B0',
        'CO2 Level': '#FF9800',
        'Gas Resistance': '#795548',
        'Pressure': '#607D8B',
        'Nitrogen Dioxide (NO2)': '#E91E63',
        'Ozone (O3)': '#00BCD4'
      };
      return colorMap[metric] || '#4FB8A3';
    };

    const color = getMetricColor(metric);

    return {
      labels,
      datasets: [
        {
          label: metric,
          data,
          borderColor: color,
          backgroundColor: color + '20', // Add transparency
          fill: true,
          tension: 0.3,
        },
      ],
    };
  } catch (error) {
    console.error('Error fetching historical data:', error);
    throw error;
  }
};

const Dashboard: React.FC = () => {
  const [selectedLocation, setSelectedLocation] = useState<string>('');
  const [selectedMetric, setSelectedMetric] = useState<string>('Temperature');
  const [dateFrom, setDateFrom] = useState<string>('');
  const [dateTo, setDateTo] = useState<string>('');
  const [currentData, setCurrentData] = useState<Record<string, number>>({});
  const [dataInfo, setDataInfo] = useState<{age: string, timestamp?: string, formattedTimestamp?: string | null, hasData: boolean}>({age: 'No data', hasData: false});
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());
  const [showChart, setShowChart] = useState<boolean>(false);
  const [chartData, setChartData] = useState<any>(null);
  const [chartError, setChartError] = useState<string>('');
  const [isLoadingChart, setIsLoadingChart] = useState<boolean>(false);

  // Use sensor data hook for real-time data
  const { data: sensorData, loading: sensorLoading, error: sensorError, refetch } = useSensorData(selectedLocation);

  useEffect(() => {
    const initializeDashboard = async () => {
      console.log('🔄 Dashboard: Initializing dashboard...');

      // Set default dates first
      const today = new Date();
      const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      setDateFrom(weekAgo.toISOString().split('T')[0]);
      setDateTo(today.toISOString().split('T')[0]);

      // Add dashboard-page class to body for background styling
      document.body.classList.add('dashboard-page');

      // Initialize location manager and get current location
      console.log('🔄 Dashboard: Initializing location manager...');
      try {
        // Force a fresh check when dashboard loads
        const currentLocation = await locationManager.forceRefresh();
        console.log('📍 Dashboard: Got location from location manager:', currentLocation);
        setSelectedLocation(currentLocation);
      } catch (error) {
        console.error('❌ Dashboard: Error initializing location manager:', error);
        // Try regular initialization as fallback
        try {
          const fallbackLocation = await locationManager.initialize();
          console.log('📍 Dashboard: Fallback location:', fallbackLocation);
          setSelectedLocation(fallbackLocation);
        } catch (fallbackError) {
          console.error('❌ Dashboard: Fallback also failed:', fallbackError);
          // Final fallback to localStorage
          const storedLocation = localStorage.getItem('userLocation') || 'basement';
          console.log('📍 Dashboard: Final fallback to stored location:', storedLocation);
          setSelectedLocation(storedLocation);
        }
      }
    };

    initializeDashboard();

    // Add listener for location changes from location manager
    const handleLocationUpdate = (newLocation: string) => {
      console.log('📡 Dashboard: Location update received:', newLocation);
      setSelectedLocation(newLocation);
      // Refresh sensor data when location changes
      if (refetch) {
        setTimeout(() => refetch(), 500); // Small delay to ensure location is set
      }
    };

    locationManager.addListener(handleLocationUpdate);

    return () => {
      document.body.classList.remove('dashboard-page');
      locationManager.removeListener(handleLocationUpdate);
    };
  }, []);

  // Update current data when sensor data changes
  useEffect(() => {
    if (sensorData) {
      const result = convertSensorDataToDashboard(sensorData);
      setCurrentData(result.data);
      setDataInfo({
        age: result.age,
        timestamp: result.timestamp,
        formattedTimestamp: result.formattedTimestamp,
        hasData: result.hasData
      });
      setLastUpdate(new Date());
    }
  }, [sensorData]);

  // REMOVED: Redundant polling - location manager handles location sync
  // The useSensorData hook already handles data refresh with its own interval

  const metrics = ['AQI', ...Object.keys(baseAirQualityStandards)];

  const getUnit = (metric: string) => {
    if (metric === 'AQI') return '';
    const standards = baseAirQualityStandards[metric as keyof typeof baseAirQualityStandards];
    return standards?.good?.unit || '';
  };



  const handleUpdateChart = async () => {
    if (!dateFrom || !dateTo) {
      alert('Please select both start and end dates');
      return;
    }

    // Use the same location as real-time data display
    const realTimeLocation = selectedLocation || 'lecture-hall'; // Default to lecture-hall if no location selected
    console.log('🔍 Using location for historical data:', realTimeLocation);

    setIsLoadingChart(true);
    setChartError('');
    setShowChart(false);

    try {
      const newChartData = await fetchHistoricalChartData(selectedMetric, dateFrom, dateTo, realTimeLocation);
      setChartData(newChartData);
      setShowChart(true);
    } catch (error: any) {
      setChartError(error.message);
      console.error('Error fetching chart data:', error);
    } finally {
      setIsLoadingChart(false);
    }
  };

  return (
    <Layout>
      <header className="welcome-section">
        <h1>Welcome to the Smart Indoor Air Quality Monitoring System</h1>
        <p className="subtitle">Track, analyze, and improve indoor air quality with ease</p>
      </header>

      <main className="dashboard-container">
        {/* Air Quality Scale */}
        <div className="quality-scale-card">
          <h2>Air Quality Scale</h2>
          <div className="scale-bar"></div>
          <div className="scale-labels">
            <span>Good</span>
            <span>Moderate</span>
            <span>Poor</span>
          </div>
        </div>

        {/* Current Location Display */}
        {selectedLocation && (
          <div className="alert alert-info mb-4 d-flex justify-content-between align-items-center">
            <div>
              <strong>Current Location:</strong> {selectedLocation.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </div>
            <small className="text-muted">
              Last updated: {lastUpdate.toLocaleTimeString()}
            </small>
          </div>
        )}

        {/* Hardware connectivity warning removed - dashboard now always shows latest data */}

        {/* No Data Warning - Only show if no data exists at all */}
        {!dataInfo.hasData && (
          <div className="alert alert-secondary mb-4">
            <div className="d-flex align-items-center">
              <i className="bi bi-info-circle me-2"></i>
              <div>
                <strong>📡 No Sensor Data Available</strong>
                <br />
                <small>No sensor data found in database. Please check hardware system connectivity and ensure sensors are transmitting data.</small>
              </div>
            </div>
          </div>
        )}

        {/* Metrics Grid */}
        <div className="metrics-grid">
          {metrics.map((metric) => {
            // Always show latest data from Firebase
            const value = dataInfo.hasData ? (currentData[metric] || 0) : 0;
            const status = getQualityStatus(metric, value);
            const unit = getUnit(metric);

            // Get max value for gauge based on metric
            const getMaxValue = (metric: string) => {
              const maxValues: { [key: string]: number } = {
                'AQI': 500,
                'Temperature': 40,
                'Humidity': 100,
                'PM2.5': 300,
                'PM10': 500,
                'CO2 Level': 2000,
                'Gas Resistance': 2000,
                'Pressure': 1050,
                'Nitrogen Dioxide (NO2)': 200,
                'Ozone (O3)': 200
              };
              return maxValues[metric] || 100;
            };

            return (
              <CircularGauge
                key={metric}
                value={value}
                maxValue={getMaxValue(metric)}
                unit={unit}
                title={metric}
                status={status as 'good' | 'moderate' | 'poor'}
                size={180}
              />
            );
          })}
        </div>

        {/* Real-time Controls */}
        <div className="card mb-4">
          <div className="card-body">
            <div className="d-flex justify-content-between align-items-center">
              <h5 className="card-title mb-0">Real-time Monitoring</h5>
              <button
                className="btn btn-outline-primary"
                onClick={async () => {
                  if (selectedLocation) {
                    try {
                      console.log('🔄 Fetching latest data for:', selectedLocation);

                      // Call backend API directly to get timestamp from response
                      const response = await fetch(`http://localhost:3003/api/sensor-data/${selectedLocation}/latest`);
                      const apiResult = await response.json();

                      console.log('🔍 Full API response:', apiResult);

                      if (apiResult.success && apiResult.data) {
                        // The timestamp is the Firebase key that represents when data was stored
                        const firebaseTimestamp = apiResult.data.timestamp;
                        console.log('🔍 Firebase timestamp key from API:', firebaseTimestamp);
                        console.log('🔍 Expected terminal timestamp: 2025-07-14 19:44:29');

                        // Parse the Firebase timestamp key to readable format
                        const parsedTimestamp = parseFirebaseTimestamp(firebaseTimestamp);
                        console.log('🔍 Parsed timestamp for display:', parsedTimestamp);

                        // Convert sensor data
                        const result = convertSensorDataToDashboard(apiResult.data);

                        setCurrentData(result.data);
                        setDataInfo({
                          age: result.age,
                          timestamp: parsedTimestamp,
                          formattedTimestamp: parsedTimestamp,
                          hasData: result.hasData
                        });
                        setLastUpdate(new Date());
                      }
                    } catch (error) {
                      console.error('Error refreshing data:', error);
                    }
                  }
                }}
                disabled={!selectedLocation || sensorLoading}
              >
                <i className="bi bi-arrow-clockwise me-2"></i>
                {sensorLoading ? 'Loading...' : 'Refresh Data'}
              </button>
            </div>
            <p className="text-muted mt-2 mb-0">
              Real-time data updates every 30 seconds from sensors. Current location: {selectedLocation || 'Not selected'}
              {dataInfo.hasData && dataInfo.timestamp && (
                <><br /><small>Timestamp: {dataInfo.formattedTimestamp || 'Processing...'}</small></>
              )}
            </p>
            {sensorError && (
              <div className="alert alert-warning mt-2 mb-0">
                <small>⚠️ {sensorError}</small>
              </div>
            )}
          </div>
        </div>

        {/* Historical Data Section */}
        <div className="trends-section card">
          <div className="card-body">
            <div className="trends-header d-flex justify-content-between align-items-center mb-3 flex-wrap">
              <h2 className="mb-2 mb-md-0">Historical Data Analysis</h2>
              <div className="trends-controls d-flex gap-2 flex-wrap">
                <select
                  value={selectedMetric}
                  onChange={(e) => setSelectedMetric(e.target.value)}
                  className="form-select"
                  style={{ minWidth: '150px' }}
                >
                  {metrics.map(metric => (
                    <option key={metric} value={metric}>{metric}</option>
                  ))}
                </select>
                <input
                  type="date"
                  value={dateFrom}
                  onChange={(e) => setDateFrom(e.target.value)}
                  className="form-control"
                  style={{ width: 'auto' }}
                />
                <input
                  type="date"
                  value={dateTo}
                  onChange={(e) => setDateTo(e.target.value)}
                  className="form-control"
                  style={{ width: 'auto' }}
                />
                <button className="btn btn-primary" onClick={handleUpdateChart}>
                  <i className="bi bi-graph-up me-2"></i>
                  Update Chart
                </button>
              </div>
            </div>

            {isLoadingChart ? (
              <div className="chart-placeholder bg-light p-4 rounded border text-center" style={{ height: '400px' }}>
                <div className="d-flex align-items-center justify-content-center h-100">
                  <div>
                    <div className="spinner-border text-primary" role="status">
                      <span className="visually-hidden">Loading...</span>
                    </div>
                    <p className="mt-3 text-muted">Loading historical data...</p>
                  </div>
                </div>
              </div>
            ) : chartError ? (
              <div className="chart-placeholder bg-light p-4 rounded border text-center" style={{ height: '400px' }}>
                <div className="d-flex align-items-center justify-content-center h-100">
                  <div>
                    <i className="bi bi-exclamation-triangle text-warning" style={{ fontSize: '3rem' }}></i>
                    <p className="mt-3 text-warning">{chartError}</p>
                    <button className="btn btn-outline-primary btn-sm" onClick={handleUpdateChart}>
                      Try Again
                    </button>
                  </div>
                </div>
              </div>
            ) : showChart && chartData ? (
              <div className="chart-container bg-light p-4 rounded border" style={{ height: '400px' }}>
                <LineChart
                  data={chartData}
                  height={350}
                />
              </div>
            ) : (
              <div className="chart-placeholder bg-light p-4 rounded border text-center" style={{ height: '400px' }}>
                <div className="d-flex align-items-center justify-content-center h-100">
                  <div>
                    <i className="bi bi-graph-up" style={{ fontSize: '3rem', color: '#6c757d' }}></i>
                    <p className="mt-3 text-muted">Select date range and click "Update Chart" to view historical data</p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </main>
    </Layout>
  );
};

export default Dashboard;
