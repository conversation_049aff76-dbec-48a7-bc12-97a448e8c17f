import React from 'react';
import Layout from '../layouts/Layout';

const AboutUs: React.FC = () => {
  return (
    <Layout showNavbar={true}>
      <div className="about-us-container">
        <div className="about-container">
          {/* LEFT SECTION */}
          <div className="left-section">
            <h1 className="main-title">AirSense Is Best Choice For You</h1>

            <div className="left-image">
              <img
                src="/air.png"
                alt="City Pollution Monitoring - Air Quality Sensors and Environmental Monitoring Systems"
                className="section-image"
              />
            </div>

            <div className="mission-card">
              <h2>Our Mission</h2>
              <p>At AirSense, we are dedicated to creating healthier indoor environments through advanced air quality monitoring technology. Our mission is to provide real-time, accurate air quality data that empowers individuals and organizations to make informed decisions about their indoor air quality.</p>
            </div>

            <div className="air-quality-matters">
              <h2>Why Air Quality Matters</h2>
              <p>Indoor air quality significantly impacts our health, productivity, and overall well-being. Poor air quality can lead to various health issues including respiratory problems, allergies, and reduced cognitive function. Our system helps you identify potential issues, monitor trends, make data-driven decisions, and create healthier spaces for work, study, and living.</p>
            </div>

            <div className="dashboard-section">
              <h2>Indoor Air Quality Dashboard</h2>
              <div className="dashboard-placeholder">
                <img
                  src="/dash.png"
                  alt="Real-Time Air Quality Monitoring Dashboard"
                  className="dashboard-image-main"
                />
              </div>
              <p className="dashboard-subtitle">Real-Time Air Quality Monitoring Dashboard</p>
            </div>
          </div>

          {/* RIGHT SECTION */}
          <div className="right-section">
            <div className="right-image">
              <img
                src="/person.png"
                alt="Healthcare Professional Using Air Quality Monitor - Professional Indoor Air Quality Assessment"
                className="section-image"
              />
            </div>

            <div className="welcome-card">
              <h2>Welcome!</h2>
              <p>We're passionate about clean air and committed to sharing the benefits of healthy indoor environments. Our page is where you'll discover our love for air quality monitoring, our mission to connect people with better air, and our dedication to fostering a vibrant community of health-conscious individuals.</p>
            </div>

            <div className="what-we-do">
              <h2>What We Do</h2>
              <p>AirSense offers a comprehensive indoor air quality monitoring system that tracks multiple environmental parameters including AQI measurements, temperature and humidity monitoring, real-time data visualization, historical data analysis, and location-based monitoring across different areas.</p>
            </div>

            <div className="monitoring-alerts">
              <div className="monitoring-card">
                <h3>Multi-Parameter Monitoring</h3>
                <ul>
                  <li>Air Quality Index (AQI)</li>
                  <li>Temperature & Humidity</li>
                  <li>CO₂ & PM2.5 Levels</li>
                  <li>Gas Resistance & Pressure</li>
                </ul>
              </div>

              <div className="alerts-card">
                <h3>Real-Time Alerts</h3>
                <p>Get instant notifications when air quality parameters exceed safe levels.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
};

export default AboutUs;
