#!/usr/bin/env node

/**
 * Clean up Mock Data from Sensor_Data Section
 * 
 * This script removes all mock/sample sensor data from the Sensor_Data section
 * while preserving the category structure and metadata.
 * 
 * Usage: node cleanup-sensor-data-mock.js
 */

const { FirebaseService, initializeFirebase } = require('./firebase');
const { getDatabase } = require('firebase-admin/database');

async function cleanupSensorDataMock() {
  console.log('🧹 Starting Sensor_Data mock data cleanup...');
  
  try {
    // Initialize Firebase connection
    console.log('🔥 Connecting to Firebase...');
    initializeFirebase();
    
    const db = getDatabase();
    
    // Get all categories
    console.log('📋 Getting Sensor_Data categories...');
    const categories = await FirebaseService.getSensorDataCategories();
    console.log('📁 Found categories:', categories);
    
    if (categories.length === 0) {
      console.log('⚠️ No categories found in Sensor_Data section');
      return;
    }
    
    // Clean up each category
    for (const category of categories) {
      console.log(`\n🧹 Cleaning up category: ${category}`);
      
      // Get all data in the category
      const categoryRef = db.ref(`Sensor_Data/${category}`);
      const snapshot = await categoryRef.once('value');
      
      if (!snapshot.exists()) {
        console.log(`⚠️ Category ${category} does not exist`);
        continue;
      }
      
      const categoryData = snapshot.val();
      const dataKeys = Object.keys(categoryData);
      
      // Filter out metadata and dailyAverage - only remove sensor readings
      const sensorDataKeys = dataKeys.filter(key => 
        key !== '_metadata' && 
        key !== 'dailyAverage'
      );
      
      console.log(`📊 Found ${sensorDataKeys.length} sensor data entries to remove`);
      
      if (sensorDataKeys.length === 0) {
        console.log(`✅ No sensor data to clean in ${category}`);
        continue;
      }
      
      // Remove each sensor data entry
      let removedCount = 0;
      for (const dataKey of sensorDataKeys) {
        try {
          await categoryRef.child(dataKey).remove();
          removedCount++;
          console.log(`🗑️ Removed: ${dataKey}`);
        } catch (error) {
          console.error(`❌ Error removing ${dataKey}:`, error.message);
        }
      }
      
      console.log(`✅ Cleaned ${category}: Removed ${removedCount}/${sensorDataKeys.length} entries`);
      
      // Update metadata to reflect cleanup
      const metadataRef = categoryRef.child('_metadata');
      await metadataRef.update({
        lastCleaned: new Date().toISOString(),
        cleanedAt: require('firebase-admin').database.ServerValue.TIMESTAMP,
        lastUpdated: require('firebase-admin').database.ServerValue.TIMESTAMP
      });
    }
    
    console.log('\n🎉 Sensor_Data mock data cleanup completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`✅ Categories processed: ${categories.length}`);
    console.log('🔧 Preserved: Category structure and metadata');
    console.log('🗑️ Removed: All sensor data entries (mock/sample data)');
    
    console.log('\n📍 Database Structure After Cleanup:');
    console.log('Sensor_Data/');
    for (const category of categories) {
      console.log(`├── ${category}/`);
      console.log('│   ├── _metadata/     # Preserved');
      console.log('│   └── dailyAverage/ # Preserved (if exists)');
    }
    
    console.log('\n🔗 Firebase Database URL: https://test-2-c0fd4-default-rtdb.asia-southeast1.firebasedatabase.app/');
    console.log('📍 Navigate to: Sensor_Data/ to verify cleanup');
    
    // Verify cleanup by checking each category
    console.log('\n🔍 Verifying cleanup...');
    for (const category of categories) {
      const data = await FirebaseService.readSensorDataFromCategory(category, 10);
      console.log(`📊 ${category}: ${data.length} sensor readings remaining`);
    }
    
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  }
}

// Handle script execution
if (require.main === module) {
  cleanupSensorDataMock();
}

module.exports = { cleanupSensorDataMock };
