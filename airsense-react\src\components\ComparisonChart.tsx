import React from 'react';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
} from 'chart.js';
import { Line } from 'react-chartjs-2';

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

interface ComparisonChartProps {
  location1: string;
  location2: string;
  metric1: string;
  metric2: string;
  height?: number;
}

const ComparisonChart: React.FC<ComparisonChartProps> = ({
  location1,
  location2,
  metric1,
  metric2,
  height = 300
}) => {
  // Generate mock hourly data for the day
  const generateHourlyData = (baseValue: number, variance: number = 5) => {
    const hours = Array.from({ length: 24 }, (_, i) => i);
    return hours.map(() => {
      const variation = (Math.random() - 0.5) * variance;
      return Math.max(0, baseValue + variation);
    });
  };

  const hours = Array.from({ length: 24 }, (_, i) => 
    `${i.toString().padStart(2, '0')}:00`
  );

  // Generate different base values for different locations
  const getBaseValue = (location: string, metric: string) => {
    const locationMultiplier = location === 'lectureHall' ? 1.1 : 
                              location === 'Office' ? 0.9 : 
                              location === 'Basement' ? 1.2 : 1.0;
    
    const baseValues: { [key: string]: number } = {
      'Temperature': 23,
      'Humidity': 45,
      'PM2.5': 35,
      'PM10': 50,
      'CO2 Level': 600,
      'Gas Resistance': 800,
      'Pressure': 1013,
      'Nitrogen Dioxide (NO2)': 25,
      'Ozone (O3)': 30
    };
    
    return (baseValues[metric] || 50) * locationMultiplier;
  };

  const data1 = generateHourlyData(getBaseValue(location1, metric1));
  const data2 = generateHourlyData(getBaseValue(location2, metric2));

  const chartData = {
    labels: hours,
    datasets: [
      {
        label: `${location1.replace(/([A-Z])/g, ' $1').trim()} - ${metric1}`,
        data: data1,
        borderColor: '#3b82f6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)',
        fill: false,
        tension: 0.3,
      },
      {
        label: `${location2.replace(/([A-Z])/g, ' $1').trim()} - ${metric2}`,
        data: data2,
        borderColor: '#10b981',
        backgroundColor: 'rgba(16, 185, 129, 0.1)',
        fill: false,
        tension: 0.3,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
        labels: {
          usePointStyle: true,
          padding: 20,
        },
      },
      title: {
        display: true,
        text: 'Daily Distribution Comparison',
        font: {
          size: 16,
          weight: 'bold',
        },
        padding: 20,
      },
      tooltip: {
        mode: 'index' as const,
        intersect: false,
        backgroundColor: 'rgba(0, 0, 0, 0.8)',
        titleColor: 'white',
        bodyColor: 'white',
        borderColor: 'rgba(255, 255, 255, 0.2)',
        borderWidth: 1,
      },
    },
    scales: {
      x: {
        display: true,
        title: {
          display: true,
          text: 'Time (Hours)',
          font: {
            weight: 'bold',
          },
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
      },
      y: {
        display: true,
        title: {
          display: true,
          text: 'Value',
          font: {
            weight: 'bold',
          },
        },
        grid: {
          display: true,
          color: 'rgba(0, 0, 0, 0.1)',
        },
        beginAtZero: true,
      },
    },
    interaction: {
      mode: 'nearest' as const,
      axis: 'x' as const,
      intersect: false,
    },
    elements: {
      point: {
        radius: 3,
        hoverRadius: 6,
      },
      line: {
        borderWidth: 2,
      },
    },
  };

  if (!location1 || !location2) {
    return (
      <div 
        className="d-flex align-items-center justify-content-center bg-light rounded"
        style={{ height: `${height}px` }}
      >
        <div className="text-center text-muted">
          <i className="bi bi-graph-up" style={{ fontSize: '3rem' }}></i>
          <h5 className="mt-3">Select both locations to view comparison chart</h5>
          <p>Choose locations and metrics to see the daily distribution comparison</p>
        </div>
      </div>
    );
  }

  return (
    <div style={{ height: `${height}px`, width: '100%' }}>
      <Line data={chartData} options={options} />
    </div>
  );
};

export default ComparisonChart;
