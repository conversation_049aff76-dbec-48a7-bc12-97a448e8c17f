/**
 * Comprehensive Database Integration Tests
 * Tests admin login/logout, location selection, and device restart scenarios
 */

const axios = require('axios');

class DatabaseIntegrationTester {
  constructor(serverUrl = 'http://localhost:3003') {
    this.serverUrl = serverUrl;
    this.testResults = [];
  }

  /**
   * Log test result
   */
  logTest(testName, success, message, data = null) {
    const result = {
      test: testName,
      success: success,
      message: message,
      data: data,
      timestamp: new Date().toISOString()
    };
    
    this.testResults.push(result);
    
    const status = success ? '✅' : '❌';
    console.log(`${status} ${testName}: ${message}`);
    
    if (data) {
      console.log('   Data:', JSON.stringify(data, null, 2));
    }
  }

  /**
   * Test admin login functionality
   */
  async testAdminLogin() {
    console.log('\n🔐 Testing Admin Login...');
    
    try {
      // Test valid login
      const response = await axios.post(`${this.serverUrl}/api/admin/login`, {
        email: '<EMAIL>',
        password: 'admin123'
      });

      if (response.data.success) {
        this.logTest('Admin Login (Valid)', true, 'Admin logged in successfully', response.data);
      } else {
        this.logTest('Admin Login (Valid)', false, 'Login failed with valid credentials', response.data);
      }

    } catch (error) {
      this.logTest('Admin Login (Valid)', false, `Login error: ${error.message}`);
    }

    try {
      // Test invalid login
      const response = await axios.post(`${this.serverUrl}/api/admin/login`, {
        email: '<EMAIL>',
        password: 'wrongpassword'
      });

      this.logTest('Admin Login (Invalid)', false, 'Invalid login should have failed', response.data);

    } catch (error) {
      if (error.response && error.response.status === 401) {
        this.logTest('Admin Login (Invalid)', true, 'Invalid login correctly rejected');
      } else {
        this.logTest('Admin Login (Invalid)', false, `Unexpected error: ${error.message}`);
      }
    }
  }

  /**
   * Test location selection functionality
   */
  async testLocationSelection() {
    console.log('\n📍 Testing Location Selection...');
    
    try {
      // Test valid location selection
      const response = await axios.post(`${this.serverUrl}/api/admin/location`, {
        location: 'deans-office',
        adminEmail: '<EMAIL>'
      });

      if (response.data.success) {
        this.logTest('Location Selection (Valid)', true, 'Location selected successfully', response.data);
      } else {
        this.logTest('Location Selection (Valid)', false, 'Location selection failed', response.data);
      }

    } catch (error) {
      this.logTest('Location Selection (Valid)', false, `Location selection error: ${error.message}`);
    }

    try {
      // Test invalid location
      const response = await axios.post(`${this.serverUrl}/api/admin/location`, {
        location: 'invalid-location',
        adminEmail: '<EMAIL>'
      });

      this.logTest('Location Selection (Invalid)', false, 'Invalid location should have failed', response.data);

    } catch (error) {
      if (error.response && error.response.status === 500) {
        this.logTest('Location Selection (Invalid)', true, 'Invalid location correctly rejected');
      } else {
        this.logTest('Location Selection (Invalid)', false, `Unexpected error: ${error.message}`);
      }
    }
  }

  /**
   * Test sensor data storage with location
   */
  async testSensorDataStorage() {
    console.log('\n📊 Testing Sensor Data Storage...');
    
    const sensorData = {
      temperature: 25.5,
      humidity: 60.2,
      co2: 850,
      pm25: 15.3,
      pm10: 22.1,
      voc: 250
    };

    try {
      const response = await axios.post(`${this.serverUrl}/api/sensor-data`, sensorData);

      if (response.data.success) {
        if (response.data.delayedStorage) {
          this.logTest('Sensor Data Storage (Active)', true, `Sensor data queued for delayed storage (${response.data.delayMinutes} min delay)`, response.data);
        } else {
          this.logTest('Sensor Data Storage (Active)', true, 'Sensor data stored successfully', response.data);
        }
      } else {
        this.logTest('Sensor Data Storage (Active)', true, `Data not stored: ${response.data.message}`, response.data);
      }

    } catch (error) {
      this.logTest('Sensor Data Storage (Active)', false, `Sensor data error: ${error.message}`);
    }
  }

  /**
   * Test admin status retrieval
   */
  async testAdminStatus() {
    console.log('\n📋 Testing Admin Status...');
    
    try {
      const response = await axios.get(`${this.serverUrl}/api/admin/status`);

      if (response.data.success) {
        this.logTest('Admin Status', true, 'Status retrieved successfully', response.data.status);
      } else {
        this.logTest('Admin Status', false, 'Failed to get status', response.data);
      }

    } catch (error) {
      this.logTest('Admin Status', false, `Status error: ${error.message}`);
    }
  }

  /**
   * Test device restart functionality
   */
  async testDeviceRestart() {
    console.log('\n📱 Testing Device Restart...');
    
    try {
      const response = await axios.post(`${this.serverUrl}/api/device/restart`);

      if (response.data.success) {
        this.logTest('Device Restart', true, 'Device restart handled successfully', response.data);
      } else {
        this.logTest('Device Restart', false, 'Device restart failed', response.data);
      }

    } catch (error) {
      this.logTest('Device Restart', false, `Device restart error: ${error.message}`);
    }
  }

  /**
   * Test admin logout functionality
   */
  async testAdminLogout() {
    console.log('\n🚪 Testing Admin Logout...');
    
    try {
      const response = await axios.post(`${this.serverUrl}/api/admin/logout`, {
        email: '<EMAIL>'
      });

      if (response.data.success) {
        this.logTest('Admin Logout', true, 'Admin logged out successfully', response.data);
      } else {
        this.logTest('Admin Logout', false, 'Logout failed', response.data);
      }

    } catch (error) {
      this.logTest('Admin Logout', false, `Logout error: ${error.message}`);
    }
  }

  /**
   * Test sensor data storage after logout (should fail)
   */
  async testSensorDataAfterLogout() {
    console.log('\n🚫 Testing Sensor Data After Logout...');

    const sensorData = {
      temperature: 25.5,
      humidity: 60.2,
      co2: 850,
      pm25: 15.3,
      pm10: 22.1,
      voc: 250
    };

    try {
      const response = await axios.post(`${this.serverUrl}/api/sensor-data`, sensorData);

      if (!response.data.success) {
        this.logTest('Sensor Data After Logout', true, 'Data correctly rejected after logout', response.data);
      } else {
        this.logTest('Sensor Data After Logout', false, 'Data should not be stored after logout', response.data);
      }

    } catch (error) {
      this.logTest('Sensor Data After Logout', false, `Unexpected error: ${error.message}`);
    }
  }

  /**
   * Test delayed storage status
   */
  async testDelayedStorageStatus() {
    console.log('\n⏰ Testing Delayed Storage Status...');

    try {
      const response = await axios.get(`${this.serverUrl}/api/delayed-storage/status`);

      if (response.data.success) {
        this.logTest('Delayed Storage Status', true, 'Status retrieved successfully', response.data.status);
      } else {
        this.logTest('Delayed Storage Status', false, 'Failed to get delayed storage status', response.data);
      }

    } catch (error) {
      this.logTest('Delayed Storage Status', false, `Status error: ${error.message}`);
    }
  }

  /**
   * Run all tests in sequence
   */
  async runAllTests() {
    console.log('🚀 Starting Database Integration Tests...\n');
    
    const startTime = Date.now();
    
    try {
      // Test sequence that simulates real usage
      await this.testAdminLogin();
      await this.testLocationSelection();
      await this.testSensorDataStorage();
      await this.testDelayedStorageStatus();
      await this.testAdminStatus();
      await this.testDeviceRestart();
      await this.testAdminLogout();
      await this.testSensorDataAfterLogout();
      
    } catch (error) {
      console.error('❌ Test suite error:', error);
    }
    
    const endTime = Date.now();
    const duration = (endTime - startTime) / 1000;
    
    this.printTestSummary(duration);
  }

  /**
   * Print test summary
   */
  printTestSummary(duration) {
    console.log('\n' + '='.repeat(60));
    console.log('📊 TEST SUMMARY');
    console.log('='.repeat(60));
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests} ✅`);
    console.log(`Failed: ${failedTests} ❌`);
    console.log(`Duration: ${duration}s`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);
    
    if (failedTests > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults
        .filter(r => !r.success)
        .forEach(r => console.log(`   - ${r.test}: ${r.message}`));
    }
    
    console.log('\n' + '='.repeat(60));
  }
}

// Run tests if this file is executed directly
async function runTests() {
  const tester = new DatabaseIntegrationTester();
  await tester.runAllTests();
  process.exit(0);
}

if (require.main === module) {
  runTests();
}

module.exports = DatabaseIntegrationTester;
