Write-Host "Starting AirSense Applications..." -ForegroundColor Green
Write-Host ""

Write-Host "Starting Main App (airsense-react) on port 5174..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd airsense-react; npm run dev" -WindowStyle Normal

Write-Host ""
Write-Host "Waiting 3 seconds before starting admin app..." -ForegroundColor Yellow
Start-Sleep -Seconds 3

Write-Host "Starting Admin App (airsense-admin) on port 3001..." -ForegroundColor Yellow
Start-Process powershell -ArgumentList "-NoExit", "-Command", "cd airsense-admin; npm start" -WindowStyle Normal

Write-Host ""
Write-Host "Both applications are starting..." -ForegroundColor Green
Write-Host ""
Write-Host "Main App: http://localhost:5174/" -ForegroundColor Cyan
Write-Host "Admin App: http://localhost:3001/" -ForegroundColor Cyan
Write-Host ""
Write-Host "Press any key to exit this window..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
