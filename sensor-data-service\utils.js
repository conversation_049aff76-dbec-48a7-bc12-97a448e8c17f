/**
 * Utility functions for the sensor data service
 */

const moment = require('moment');

/**
 * Normalize timestamp format for consistent handling
 * @param {string} timestamp - The timestamp to normalize
 * @returns {string} - Normalized ISO timestamp
 */
function normalizeTimestamp(timestamp) {
  if (!timestamp) return new Date().toISOString();
  
  // If timestamp contains underscores (Firebase safe format), convert back to standard format
  if (typeof timestamp === 'string' && timestamp.includes('_')) {
    timestamp = timestamp.replace(/_/g, ':');
  }
  
  // Try to parse with moment
  const parsedTime = moment(timestamp);
  
  // If valid, return ISO string, otherwise return current time
  return parsedTime.isValid() ? parsedTime.toISOString() : new Date().toISOString();
}

/**
 * Convert timestamp to Firebase-safe format
 * @param {string} timestamp - ISO timestamp
 * @returns {string} - Firebase-safe timestamp
 */
function toFirebaseTimestamp(timestamp) {
  if (!timestamp) return new Date().toISOString().replace(/[.:#$[\]]/g, '_');
  return timestamp.replace(/[.:#$[\]]/g, '_');
}

/**
 * Convert Firebase-safe timestamp back to ISO format
 * @param {string} firebaseTimestamp - Firebase-safe timestamp
 * @returns {string} - ISO timestamp
 */
function fromFirebaseTimestamp(firebaseTimestamp) {
  if (!firebaseTimestamp) return null;
  // Replace underscores with colons
  return firebaseTimestamp.replace(/_/g, ':');
}

module.exports = {
  normalizeTimestamp,
  toFirebaseTimestamp,
  fromFirebaseTimestamp
};